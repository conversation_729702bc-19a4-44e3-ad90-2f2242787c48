<#/*THIS IS A T4 FILE - see t4_text_templating.md for what it is and how to run codegen*/#>
<#@ template debug="True" #>
<#@ output extension=".gen.cs" encoding="utf-8" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>

//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections/FixedStringFormatMethods.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using Unity.Collections.LowLevel.Unsafe;

namespace Unity.Collections
{
    /// <summary>
    /// Provides extension methods for FixedString*N*Bytes.
    /// </summary>
    public unsafe static partial class FixedStringMethods
    {
<#
    for (var ARGS = 1; ARGS <= 10; ++ARGS)
    {
        var TYPES    = String.Join(", ", Enumerable.Range(0, ARGS).Select(n => $"T{n}"));
        var PARAMS   = String.Join(", ", Enumerable.Range(0, ARGS).Select(n => $"in T{n} arg{n}"));
        var ARGNAMES = String.Join(", ", Enumerable.Range(0, ARGS).Select(n => $"arg{n}"));
        var TxDOCS   = String.Join("\r\n        /// ", Enumerable.Range(0, ARGS).Select(n => $"<typeparam name=\"T{n}\">The type of value to interpolate into the format string.</typeparam>"));
        var ARGxDOCS = String.Join("\r\n        /// ", Enumerable.Range(0, ARGS).Select(n => $"<param name=\"arg{n}\">A FixedString*N*Bytes to interpolate into the format string.</param>"));
        var BCOMPAT  = String.Join(", ", Enumerable.Range(0, ARGS).Select(n => $"typeof(FixedString128Bytes /*T{n}*/)"));
#>
        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <#=TxDOCS#>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <#=ARGxDOCS#>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), <#=BCOMPAT#> })]
        public static unsafe FormatError AppendFormat<T, U, <#=TYPES#>>(ref this T dest, in U format, <#=PARAMS#>)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
<#
        for (var a = 0; a < ARGS; ++a)
            WriteLine("            where T{0} : unmanaged, INativeList<byte>, IUTF8Bytes", a);
#>
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
<#
        for(var a = 0; a < ARGS; ++a)
        {
            WriteLine($"                            case {a}: err = dest.Append(in arg{a}); break;");
        }
#>
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

<#
    }
#>

    }
}
