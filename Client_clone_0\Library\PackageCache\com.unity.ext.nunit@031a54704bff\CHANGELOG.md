# Changelog

## [2.0.5] - 2023-06-28
- Fixing bug for InstanceID as struct changes

## [2.0.4] - 2023-06-21
- Added support for InstanceID as struct changes

## [2.0.3] - 2022-05-31
- Added unique test ID for repeated tests
- Added ContainKey and ContainValue constaints for dictionaries
- Fixed unexpected null exception when using EmptyConstraint with a null object

## [2.0.2] - 2021-10-19
- STAR reviews updates.

## [2.0.1] - 2021-09-23
- Minumum unity version lowered to 2019.4

## [2.0.0] - 2021-06-23
- Upgrade to dotnet 4.0

## [1.0.6] - 2020-11-30
- isExplicitlyReferenced set to 0 (case 1296162)
## [1.0.5] - 2020-11-04
- Removed pdb files

## [1.0.4] - 2020-11-03
- Added the portable-pdb (DSTR-37)

## [1.0.3] - 2020-10-30
- Fixed being able to load mdb or portable-pdb symbolsbug (DSTR-37)
- Minimum unity version updated (case 1279253)

## [1.0.2] - 2019-12-04

- Added missed metafiles

## [0.0.1] - 2019-02-21

### This is the first release of *Unity Package com.unity.ext.nunit*.

- Migrated the custom version of nunit from inside of unity.
