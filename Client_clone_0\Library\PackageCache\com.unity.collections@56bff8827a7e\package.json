{"name": "com.unity.collections", "displayName": "Collections", "version": "2.5.1", "unity": "2022.3", "unityRelease": "11f1", "dependencies": {"com.unity.burst": "1.8.17", "com.unity.nuget.mono-cecil": "1.11.4", "com.unity.test-framework": "1.4.5", "com.unity.test-framework.performance": "3.0.3"}, "description": "A C# collections library providing data structures that can be used in jobs, and optimized by Burst compiler.", "keywords": ["dots", "collections", "unity"], "_upm": {"changelog": "### Changed\n* Updated Burst dependency to version 1.8.17\n* Updated Unity Test Framework dependency to version 1.4.5\n* Updated entities packages dependencies\n\n### Fixed\n* Certain cases would cause an ILPostProcessor to fail, blocking compilation, but no more."}, "upmCi": {"footprint": "35e38e7ad66aabae7ebf2bb73418e2225dbd8fec"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/dots.git", "type": "git", "revision": "5b0dea6b455f5df005c19fa984ddfa237d6cd707"}, "_fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0"}