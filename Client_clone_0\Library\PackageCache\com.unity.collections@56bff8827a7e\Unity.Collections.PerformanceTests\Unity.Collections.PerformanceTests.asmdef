{"name": "Unity.Collections.PerformanceTests", "rootNamespace": "", "references": ["Unity.Collections", "Unity.PerformanceTesting", "Unity.Burst", "Unity.Jobs", "UnityEngine.TestRunner", "UnityEditor.TestRunner", "Unity.Mathematics"], "includePlatforms": [], "excludePlatforms": ["iOS"], "allowUnsafeCode": true, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}