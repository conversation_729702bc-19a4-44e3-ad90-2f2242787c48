using System.Collections.Generic;
using UnityEngine;

public class MultiplayerManager : MonoBeh<PERSON><PERSON>
{
    [Header("Player Settings")]
    public GameObject playerPrefab;
    
    private Dictionary<string, GameObject> _otherPlayers = new Dictionary<string, GameObject>();
    
    private void Start()
    {
        NetworkManager.Instance.OnMessageReceived += ProcessServerMessage;
    }
    
    private void ProcessServerMessage(string message)
    {
        // Format: "playerID1:X,Y,Z;playerID2:X,Y,Z;..."
        string[] players = message.Split(';', System.StringSplitOptions.RemoveEmptyEntries);
        
        foreach (var playerData in players)
        {
            string[] parts = playerData.Split(':');
            if (parts.Length == 2)
            {
                string playerId = parts[0];
                string[] pos = parts[1].Split(',');
                
                if (pos.Length == 3 && 
                    float.TryParse(pos[0], out float x) &&
                    float.TryParse(pos[1], out float y) &&
                    float.TryParse(pos[2], out float z))
                {
                    Vector3 position = new Vector3(x, y, z);
                    UpdateOtherPlayer(playerId, position);
                }
            }
        }
    }
    
    private void UpdateOtherPlayer(string playerId, Vector3 position)
    {
        // Không update chính mình
        if (playerId == NetworkManager.Instance.GetMyPlayerId()) return;
        
        if (!_otherPlayers.ContainsKey(playerId))
        {
            // Spawn player mới
            GameObject newPlayer = Instantiate(playerPrefab);
            newPlayer.name = $"RemotePlayer_{playerId}";
            _otherPlayers[playerId] = newPlayer;
        }
        
        // Update vị trí với smooth movement
        StartCoroutine(SmoothMove(_otherPlayers[playerId], position));
    }
    
    private System.Collections.IEnumerator SmoothMove(GameObject player, Vector3 targetPos)
    {
        Vector3 startPos = player.transform.position;
        float duration = 0.1f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            player.transform.position = Vector3.Lerp(startPos, targetPos, t);
            yield return null;
        }
        
        player.transform.position = targetPos;
    }
    
    private void OnDestroy()
    {
        if (NetworkManager.Instance != null)
            NetworkManager.Instance.OnMessageReceived -= ProcessServerMessage;
            
        // Cleanup
        foreach (var player in _otherPlayers.Values)
        {
            if (player != null) Destroy(player);
        }
        _otherPlayers.Clear();
    }
}