using System.Collections.Generic;
using UnityEngine;

public class MultiplayerManager : MonoBeh<PERSON>our
{
    [Header("Player Settings")]
    public GameObject playerPrefab;
    public GameObject remotePlayerPrefab;

    private Dictionary<string, GameObject> _otherPlayers = new Dictionary<string, GameObject>();

    private void Start()
    {
        if (NetworkManager.Instance == null)
        {
            Debug.LogError("NetworkManager.Instance is null! Make sure NetworkManager is in the scene.");
            return;
        }

        if (playerPrefab == null)
        {
            Debug.LogError("PlayerPrefab is not assigned in MultiplayerManager!");
            return;
        }

        if (remotePlayerPrefab == null)
        {
            Debug.LogWarning("RemotePlayerPrefab not assigned, using playerPrefab for remote players");
            remotePlayerPrefab = playerPrefab;
        }

        Debug.Log("MultiplayerManager started, subscribing to NetworkManager events.");
        NetworkManager.Instance.OnMessageReceived += ProcessServerMessage;
    }

    private void ProcessServerMessage(string message)
    {
        Debug.Log($"Processing server message: {message}");

        // Format: "playerID1:X,Y,Z;playerID2:X,Y,Z;..."
        string[] players = message.Split(';', System.StringSplitOptions.RemoveEmptyEntries);
        Debug.Log($"Found {players.Length} players in message");

        foreach (var playerData in players)
        {
            string[] parts = playerData.Split(':');
            if (parts.Length == 2)
            {
                string playerId = parts[0];
                string[] pos = parts[1].Split(',');

                if (pos.Length == 3 &&
                    float.TryParse(pos[0], out float x) &&
                    float.TryParse(pos[1], out float y) &&
                    float.TryParse(pos[2], out float z))
                {
                    Vector3 position = new Vector3(x, y, z);
                    Debug.Log($"Updating player {playerId} to position {position}");
                    UpdateOtherPlayer(playerId, position);
                }
                else
                {
                    Debug.LogWarning($"Failed to parse position for player {playerId}: {parts[1]}");
                }
            }
            else
            {
                Debug.LogWarning($"Invalid player data format: {playerData}");
            }
        }
    }

    private void UpdateOtherPlayer(string playerId, Vector3 position)
    {
        // Không update chính mình
        if (playerId == NetworkManager.Instance.GetMyPlayerId())
        {
            Debug.Log($"Skipping self update for {playerId}");
            return;
        }

        if (!_otherPlayers.ContainsKey(playerId))
        {
            // Spawn remote player
            Debug.Log($"Spawning new remote player: {playerId}");
            GameObject newPlayer = Instantiate(remotePlayerPrefab);
            newPlayer.name = $"RemotePlayer_{playerId}";

            // Add RemotePlayerController nếu chưa có
            RemotePlayerController rpc = newPlayer.GetComponent<RemotePlayerController>();
            if (rpc == null)
            {
                rpc = newPlayer.AddComponent<RemotePlayerController>();
            }

            // Remove PlayerController nếu có (để tránh nhận input)
            PlayerController pc = newPlayer.GetComponent<PlayerController>();
            if (pc != null)
            {
                Destroy(pc);
            }

            _otherPlayers[playerId] = newPlayer;
        }

        // Update vị trí
        Debug.Log($"Moving player {playerId} to {position}");
        RemotePlayerController remoteController = _otherPlayers[playerId].GetComponent<RemotePlayerController>();
        if (remoteController != null)
        {
            remoteController.UpdatePosition(position);
        }
        else
        {
            // Fallback to direct position update
            _otherPlayers[playerId].transform.position = position;
        }
    }



    private void OnDestroy()
    {
        if (NetworkManager.Instance != null)
            NetworkManager.Instance.OnMessageReceived -= ProcessServerMessage;

        // Cleanup
        foreach (var player in _otherPlayers.Values)
        {
            if (player != null) Destroy(player);
        }
        _otherPlayers.Clear();
    }
}