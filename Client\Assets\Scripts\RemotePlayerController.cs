using UnityEngine;

public class RemotePlayerController : MonoBehaviour
{
    [Header("Visual")]
    public Color remotePlayerColor = Color.red;
    
    private SpriteRenderer _spriteRenderer;
    
    private void Start()
    {
        // Đ<PERSON>i màu để phân biệt với local player
        _spriteRenderer = GetComponent<SpriteRenderer>();
        if (_spriteRenderer != null)
        {
            _spriteRenderer.color = remotePlayerColor;
        }
        
        // Đảm bảo remote player không có PlayerController
        PlayerController pc = GetComponent<PlayerController>();
        if (pc != null)
        {
            Destroy(pc);
        }
        
        Debug.Log($"Remote player {gameObject.name} initialized");
    }
    
    public void UpdatePosition(Vector3 newPosition)
    {
        // Smooth movement cho remote player
        Start<PERSON><PERSON>utine(SmoothMove(newPosition));
    }
    
    private System.Collections.IEnumerator SmoothMove(Vector3 targetPos)
    {
        Vector3 startPos = transform.position;
        float duration = 0.1f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            transform.position = Vector3.Lerp(startPos, targetPos, t);
            yield return null;
        }
        
        transform.position = targetPos;
    }
}
