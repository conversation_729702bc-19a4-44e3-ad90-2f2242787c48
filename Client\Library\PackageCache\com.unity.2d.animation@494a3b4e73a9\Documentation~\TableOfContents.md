* [Introduction to 2D Animation](index)
  * [What's new](whats-new)
  * [2D Animation Asset Upgrader](AssetUpgrader)
* [Preparing and importing artwork](PreparingArtwork)
  * [Sprite Skin component](SpriteSkin)
* [Skinning editor](SkinningEditor)
  * [Tool Preferences](ToolPref)
  * [Editor tools and shortcuts](SkinEdToolsShortcuts)
  * [Sprite Visibility panel](SpriteVis)
* [Actor rigging and weighing workflow](CharacterRig)
* [Animating an actor](Animating-actor)
  * [2D Inverse Kinematics](2DIK)
* [Sprite Swap](SpriteSwapLanding)
  * [Introduction to Sprite Swap](SpriteSwapIntro)
  * [Sprite Library Asset in Unity](SL-Asset)
  * [Sprite Library Editor fundamentals](SL-Editor)
    * [Sprite Library Editor reference](SL-Editor-UI)
  * [Overrides to the Main Library](SL-Main-Library)
  * [Drag sprites to create or edit Categories and Labels](SL-Drag)
  * [Sprite Library component in Unity](SL-component)
  * [Sprite Resolver component in Unity](SL-Resolver)
  * [Setting up for Sprite Swap](SpriteSwapSetup)
    * [How to swap individual Sprites](CharacterParts)
    * [Swapping Sprite Library Assets](SLASwap)
* [Importing Samples](Examples)
  * [Simple](ex-simple)
  * [Single Skinned Sprite](ex-single-skinned-sprite)
  * [Character](ex-psd-importer)
* [Sprite Swap sample projects](ex-sprite-swap)
  * [Skeleton Sharing](ex-skeleton-sharing)
  * [Runtime Swap](ex-runtime-swap)