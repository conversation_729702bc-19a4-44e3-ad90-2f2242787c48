using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

public class NetworkManager : MonoBehaviour
{
    [Header("Network Settings")]
    public string serverIP = "127.0.0.1";
    public int serverPort = 5000;

    private UdpClient _udpClient;
    private IPEndPoint _serverEP;
    private string _myPlayerId;

    public static NetworkManager Instance { get; private set; }

    public event Action<string> OnMessageReceived;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        ConnectToServer();
    }

    private void ConnectToServer()
    {
        _serverEP = new IPEndPoint(IPAddress.Parse(serverIP), serverPort);
        _udpClient = new UdpClient();
        _myPlayerId = GeneratePlayerId();

        SendMessage("CONNECT");
        ReceiveLoop();
    }

    private async void ReceiveLoop()
    {
        while (_udpClient != null)
        {
            try
            {
                UdpReceiveResult result = await _udpClient.ReceiveAsync();
                string message = Encoding.UTF8.GetString(result.Buffer);
                OnMessageReceived?.Invoke(message);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Receive Error: {ex.Message}");
                break;
            }
        }
    }

    public void SendMessage(string message)
    {
        if (_udpClient == null) return;

        byte[] data = Encoding.UTF8.GetBytes(message);
        _udpClient.Send(data, data.Length, _serverEP);
    }

    public string GetMyPlayerId() => _myPlayerId;

    private string GeneratePlayerId()
    {
        return $"Player_{System.Guid.NewGuid().ToString("N")[..8]}";
    }

    private void OnApplicationQuit()
    {
        SendMessage($"DISCONNECT:{_myPlayerId}");
        _udpClient?.Close();
    }
}