{"name": "UnityEngine.UI.Tests", "references": ["UnityEngine.UI", "UnityEngine.TestRunner", "UnityEditor.TestRunner"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [{"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "PACKAGE_PHYSICS"}, {"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "PACKAGE_PHYSICS2D"}, {"name": "com.unity.modules.animation", "expression": "1.0.0", "define": "PACKAGE_ANIMATION"}, {"name": "com.unity.inputsystem", "expression": "1.7.0", "define": "PACKAGE_INPUTSYSTEM"}], "noEngineReferences": false}