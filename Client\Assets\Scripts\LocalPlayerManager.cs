using UnityEngine;

public class LocalPlayerManager : MonoBehaviour
{
    [<PERSON><PERSON>("Local Player Settings")]
    public GameObject localPlayerPrefab;
    public Vector3 spawnPosition = Vector3.zero;
    
    private GameObject _localPlayer;
    
    private void Start()
    {
        SpawnLocalPlayer();
    }
    
    private void SpawnLocalPlayer()
    {
        if (localPlayerPrefab == null)
        {
            Debug.LogError("LocalPlayerPrefab not assigned!");
            return;
        }
        
        // Spawn local player
        _localPlayer = Instantiate(localPlayerPrefab, spawnPosition, Quaternion.identity);
        _localPlayer.name = "LocalPlayer";
        
        // Đảm bảo local player có PlayerController và isLocalPlayer = true
        PlayerController pc = _localPlayer.GetComponent<PlayerController>();
        if (pc == null)
        {
            pc = _localPlayer.AddComponent<PlayerController>();
        }
        pc.isLocalPlayer = true;
        
        Debug.Log("Local player spawned successfully");
    }
    
    public GameObject GetLocalPlayer()
    {
        return _localPlayer;
    }
}
