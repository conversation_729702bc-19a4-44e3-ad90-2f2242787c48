Using pre-set license
Built from '6000.0/respin/6000.0.51f1-a206c6c19c75' branch; Version is '6000.0.51f1 (01c3ff5872c5) revision 115711'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32521 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-13T19:04:55Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.51f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Frank Work All/F0/Client
-logFile
Logs/AssetImportWorker1.log
-srvPort
49969
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/<PERSON> Work All/F0/Client
D:/<PERSON> Work All/F0/Client
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [1980]  Target information:

Player connection [1980]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1239641243 [EditorId] 1239641243 [Version] 1048832 [Id] WindowsEditor(7,tqh) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [1980]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1239641243 [EditorId] 1239641243 [Version] 1048832 [Id] WindowsEditor(7,tqh) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [1980] Host joined multi-casting on [***********:54997]...
Player connection [1980] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 0.95 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.51f1 (01c3ff5872c5)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Frank Work All/F0/Client/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56456
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.006617 seconds.
- Loaded All Assemblies, in  0.475 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 301 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.718 seconds
Domain Reload Profiling: 1191ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (200ms)
		LoadAssemblies (146ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (193ms)
				TypeCache.ScanAssembly (177ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (718ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (675ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (408ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (132ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.612 seconds
Refreshing native plugins compatible for Editor in 0.77 ms, found 1 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.493 seconds
Domain Reload Profiling: 1102ms
	BeginReloadAssembly (204ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (307ms)
		LoadAssemblies (299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (121ms)
			TypeCache.Refresh (93ms)
				TypeCache.ScanAssembly (79ms)
			BuildScriptInfoCaches (22ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (494ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (380ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (190ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 0.85 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 2355 unused Assets / (1.1 MB). Loaded Objects now: 2845.
Memory consumption went from 73.6 MB to 72.5 MB.
Total: 6.599900 ms (FindLiveObjects: 0.291600 ms CreateObjectMapping: 0.220600 ms MarkObjects: 5.182900 ms  DeleteObjects: 0.903700 ms)

========================================================================
Received Import Request.
  Time since last request: 402051.096391 seconds.
  path: Assets/Scripts/Player.prefab
  artifactKey: Guid(f22165843514b3247aca18205438880a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Player.prefab using Guid(f22165843514b3247aca18205438880a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'afef633a8ae361c592d4a82f1a18d9dd') in 0.1219337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

