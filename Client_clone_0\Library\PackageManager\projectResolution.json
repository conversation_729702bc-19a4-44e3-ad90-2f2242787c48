{"context": {"projectPath": "D:/<PERSON> All/F0/Client_clone_0/Packages", "unityVersion": "6000.0.51f1"}, "inputs": ["D:\\Frank Work All\\F0\\Client_clone_0\\Packages\\manifest.json", "D:\\Frank Work All\\F0\\Client_clone_0\\Packages\\packages-lock.json", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.unity.feature.2d@2.0.1": {"name": "com.unity.feature.2d", "displayName": "2D", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.feature.2d@dd1ea8910f12", "fingerprint": "dd1ea8910f12f021c166e8d0d78de44f1390ff6b", "version": "2.0.1", "source": "builtin", "testable": false}, "com.unity.ide.rider@3.0.36": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db", "fingerprint": "4d374c7eb6db6907c7e6925e3086c3c73f926e13", "editorCompatibility": "2019.4.6f1", "version": "3.0.36", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82", "fingerprint": "a0f5d16b3c8274d62d6bf6afdaa2eeef242bbd2b", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.test-framework@1.5.1": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918", "fingerprint": "dfdbd02f59180baa5668370b8f71f6fbeaa3b032", "editorCompatibility": "2022.3.0a1", "version": "1.5.1", "source": "builtin", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.ext.nunit@031a54704bff", "fingerprint": "031a54704bffe39e6a0324909f8eaa4565bdebf2", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "builtin", "testable": false}, "com.unity.2d.animation@10.1.4": {"name": "com.unity.2d.animation", "displayName": "2D Animation", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9", "fingerprint": "494a3b4e73a9ae26677ef6e9fd6bff4ca643770a", "editorCompatibility": "2023.1.0a1", "version": "10.1.4", "source": "registry", "testable": false}, "com.unity.2d.pixel-perfect@5.0.3": {"name": "com.unity.2d.pixel-perfect", "displayName": "2D Pixel Perfect", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.pixel-perfect@e3ae982b672d", "fingerprint": "e3ae982b672dc7cca42a6303bdf53b84c69991da", "editorCompatibility": "2021.1.0a1", "version": "5.0.3", "source": "registry", "testable": false}, "com.unity.2d.psdimporter@9.0.3": {"name": "com.unity.2d.psdimporter", "displayName": "2D PSD Importer", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.psdimporter@676bae148e11", "fingerprint": "676bae148e11de9a02db5a3614b8c56e4f0f44ac", "editorCompatibility": "2023.1.0a1", "version": "9.0.3", "source": "registry", "testable": false}, "com.unity.2d.sprite@1.0.0": {"name": "com.unity.2d.sprite", "displayName": "2D Sprite", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.sprite@a1146c20a947", "fingerprint": "a1146c20a94767d627b9db262ae136cedef9c2c7", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.spriteshape@10.0.7": {"name": "com.unity.2d.spriteshape", "displayName": "2D SpriteShape", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.spriteshape@9e35352ae135", "fingerprint": "9e35352ae135f602746220e7edc09eb95bbec530", "editorCompatibility": "2023.1.0a1", "version": "10.0.7", "source": "registry", "testable": false}, "com.unity.2d.tilemap@1.0.0": {"name": "com.unity.2d.tilemap", "displayName": "2D Tilemap Editor", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.tilemap@91e7def251e0", "fingerprint": "91e7def251e0de922ba309ad7aa18299b1d60ca7", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.tilemap.extras@4.1.0": {"name": "com.unity.2d.tilemap.extras", "displayName": "2D Tilemap Extras", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.tilemap.extras@13634da7dbe0", "fingerprint": "13634da7dbe06c39bac6bbe2d1a166cf91f58ad7", "editorCompatibility": "6000.0.0a1", "version": "4.1.0", "source": "registry", "testable": false}, "com.unity.2d.aseprite@1.1.9": {"name": "com.unity.2d.aseprite", "displayName": "2D Aseprite Importer", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.aseprite@996e69f78764", "fingerprint": "996e69f78764999291cc161902e252662ddc9485", "editorCompatibility": "2021.3.15f1", "version": "1.1.9", "source": "registry", "testable": false}, "com.unity.2d.common@9.0.7": {"name": "com.unity.2d.common", "displayName": "2D Common", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b", "fingerprint": "bb1fc9b3d81b3bb452c6708e8c088fe4224a0369", "editorCompatibility": "2023.1.0a1", "version": "9.0.7", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.burst@1.8.21": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.burst@59eb6f11d242", "fingerprint": "59eb6f11d2422f95682320d9daa3e79fdb076744", "editorCompatibility": "2020.3.0a1", "version": "1.8.21", "source": "registry", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.test-framework.performance@3.1.0": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "D:\\Frank Work All\\F0\\Client_clone_0\\Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed", "fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50", "editorCompatibility": "2020.3.0a1", "version": "3.1.0", "source": "registry", "testable": false}}}