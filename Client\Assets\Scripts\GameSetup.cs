using UnityEngine;

public class GameSetup : MonoBehaviour
{
    [Header("Setup Check")]
    public bool autoSetup = true;

    private void Start()
    {
        if (autoSetup)
        {
            CheckAndSetupGame();
        }
    }

    private void CheckAndSetupGame()
    {
        Debug.Log("=== Game Setup Check ===");

        // Check NetworkManager
        if (NetworkManager.Instance == null)
        {
            Debug.LogError("NetworkManager not found! Creating one...");
            GameObject nmGO = new GameObject("NetworkManager");
            nmGO.AddComponent<NetworkManager>();
        }
        else
        {
            Debug.Log("✓ NetworkManager found");
        }

        // Check MultiplayerManager
        MultiplayerManager mm = FindObjectOfType<MultiplayerManager>();
        if (mm == null)
        {
            Debug.LogError("MultiplayerManager not found! Creating one...");
            GameObject mmGO = new GameObject("MultiplayerManager");
            mm = mmGO.AddComponent<MultiplayerManager>();
        }
        else
        {
            Debug.Log("✓ MultiplayerManager found");
        }

        // Check if playerPrefab is assigned
        if (mm.playerPrefab == null)
        {
            Debug.LogError("PlayerPrefab not assigned in MultiplayerManager!");
            // Try to find Player prefab
            GameObject prefab = Resources.Load<GameObject>("Player");
            if (prefab != null)
            {
                mm.playerPrefab = prefab;
                Debug.Log("✓ Auto-assigned Player prefab from Resources");
            }
        }
        else
        {
            Debug.Log("✓ PlayerPrefab assigned");
        }

        // Check LocalPlayerManager
        LocalPlayerManager lpm = FindObjectOfType<LocalPlayerManager>();
        if (lpm == null)
        {
            Debug.LogError("LocalPlayerManager not found! Creating one...");
            GameObject lpmGO = new GameObject("LocalPlayerManager");
            lpm = lpmGO.AddComponent<LocalPlayerManager>();
        }
        else
        {
            Debug.Log("✓ LocalPlayerManager found");
        }

        // Check if localPlayerPrefab is assigned
        if (lpm.localPlayerPrefab == null)
        {
            Debug.LogError("LocalPlayerPrefab not assigned in LocalPlayerManager!");
        }
        else
        {
            Debug.Log("✓ LocalPlayerPrefab assigned");
        }

        Debug.Log("=== Setup Check Complete ===");
    }
}
