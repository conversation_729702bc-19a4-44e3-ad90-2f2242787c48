{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 31688, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 31688, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 31688, "tid": 92, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 31688, "tid": 92, "ts": 1755111601569975, "dur": 953, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 31688, "tid": 92, "ts": 1755111601575437, "dur": 1011, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 31688, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 31688, "tid": 1, "ts": 1755111601197501, "dur": 5803, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 31688, "tid": 1, "ts": 1755111601203308, "dur": 32874, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 31688, "tid": 1, "ts": 1755111601236194, "dur": 29571, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 31688, "tid": 92, "ts": 1755111601576453, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 31688, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601195000, "dur": 11338, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601206341, "dur": 353163, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601207454, "dur": 3029, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601210491, "dur": 1873, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601212368, "dur": 345, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601212717, "dur": 16, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601212734, "dur": 57, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601212795, "dur": 2, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601212797, "dur": 388, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213190, "dur": 100, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213292, "dur": 3, "ph": "X", "name": "ProcessMessages 2813", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213296, "dur": 41, "ph": "X", "name": "ReadAsync 2813", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213339, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213341, "dur": 37, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213380, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213382, "dur": 36, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213420, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213422, "dur": 36, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213460, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213462, "dur": 38, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213501, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213503, "dur": 67, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213573, "dur": 62, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213638, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213640, "dur": 72, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213715, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213718, "dur": 46, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213766, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213768, "dur": 44, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213815, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213817, "dur": 33, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213853, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213896, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213898, "dur": 39, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213941, "dur": 38, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213981, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601213983, "dur": 35, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214020, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214022, "dur": 53, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214078, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214121, "dur": 11, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214133, "dur": 53, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214191, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214232, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214233, "dur": 58, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214294, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214295, "dur": 33, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214331, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214332, "dur": 32, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214366, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214368, "dur": 32, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214402, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214403, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214438, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214440, "dur": 33, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214476, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214511, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214513, "dur": 30, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214544, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214546, "dur": 31, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214581, "dur": 33, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214616, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214617, "dur": 35, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214655, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214656, "dur": 29, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214690, "dur": 35, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214727, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214728, "dur": 32, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214763, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214764, "dur": 29, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214797, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214836, "dur": 33, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214871, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214872, "dur": 34, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214909, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214910, "dur": 33, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214946, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214947, "dur": 32, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601214982, "dur": 27, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215013, "dur": 34, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215050, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215051, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215086, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215087, "dur": 33, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215123, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215124, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215157, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215159, "dur": 33, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215194, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215196, "dur": 32, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215232, "dur": 34, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215268, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215269, "dur": 31, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215303, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215304, "dur": 35, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215342, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215343, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215374, "dur": 31, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215408, "dur": 35, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215446, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215447, "dur": 33, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215482, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215483, "dur": 45, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215530, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215532, "dur": 33, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215567, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215570, "dur": 29, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215602, "dur": 30, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215636, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215672, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215673, "dur": 34, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215709, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215711, "dur": 34, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215747, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215748, "dur": 34, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215785, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215787, "dur": 30, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215819, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215820, "dur": 31, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215854, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215855, "dur": 34, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215891, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215893, "dur": 33, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215929, "dur": 35, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215966, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601215968, "dur": 34, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216004, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216006, "dur": 29, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216036, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216038, "dur": 31, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216073, "dur": 33, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216107, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216109, "dur": 32, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216145, "dur": 32, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216181, "dur": 31, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216214, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216216, "dur": 31, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216249, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216251, "dur": 30, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216284, "dur": 36, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216323, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216325, "dur": 33, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216363, "dur": 34, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216399, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216401, "dur": 35, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216438, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216439, "dur": 28, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216471, "dur": 32, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216505, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216507, "dur": 46, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216556, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216605, "dur": 32, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216639, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216641, "dur": 31, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216675, "dur": 36, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216713, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216715, "dur": 29, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216747, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216784, "dur": 34, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216820, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216821, "dur": 33, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216857, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216858, "dur": 32, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216893, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216894, "dur": 30, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216927, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216928, "dur": 29, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216961, "dur": 28, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601216992, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217028, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217030, "dur": 72, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217104, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217106, "dur": 49, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217158, "dur": 1, "ph": "X", "name": "ProcessMessages 1098", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217161, "dur": 42, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217206, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217208, "dur": 41, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217253, "dur": 51, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217307, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217310, "dur": 45, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217357, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217359, "dur": 35, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217398, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217436, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217438, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217470, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217471, "dur": 32, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217508, "dur": 34, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217544, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217545, "dur": 35, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217582, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217584, "dur": 34, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217620, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217622, "dur": 36, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217660, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217662, "dur": 30, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217694, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217695, "dur": 31, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217729, "dur": 36, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217767, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217769, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217805, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217806, "dur": 33, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217842, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217844, "dur": 35, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217881, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217883, "dur": 33, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217918, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601217919, "dur": 114, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218037, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218100, "dur": 2, "ph": "X", "name": "ProcessMessages 1618", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218103, "dur": 34, "ph": "X", "name": "ReadAsync 1618", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218139, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218141, "dur": 41, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218185, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218187, "dur": 40, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218229, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218231, "dur": 49, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218283, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218286, "dur": 39, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218327, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218329, "dur": 45, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218377, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218378, "dur": 31, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218412, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218414, "dur": 45, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218461, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218463, "dur": 38, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218504, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218506, "dur": 47, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218555, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218558, "dur": 39, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218599, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218601, "dur": 37, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218640, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218642, "dur": 39, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218685, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218686, "dur": 46, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218735, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218737, "dur": 40, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218779, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218781, "dur": 43, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218827, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218830, "dur": 39, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218871, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218874, "dur": 48, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218924, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218926, "dur": 42, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218971, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601218973, "dur": 43, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219018, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219021, "dur": 35, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219059, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219061, "dur": 48, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219112, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219114, "dur": 41, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219157, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219159, "dur": 46, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219208, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219210, "dur": 49, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219262, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219264, "dur": 27, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219294, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219296, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219333, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219335, "dur": 33, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219370, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219372, "dur": 36, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219410, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219412, "dur": 30, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219444, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219446, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219483, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219517, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219519, "dur": 34, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219555, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219557, "dur": 35, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219594, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219596, "dur": 33, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219631, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219634, "dur": 30, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219666, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219668, "dur": 36, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219706, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219708, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219740, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219742, "dur": 36, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219781, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219783, "dur": 32, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219817, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219819, "dur": 35, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219856, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219858, "dur": 35, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219896, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219899, "dur": 39, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219940, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219942, "dur": 38, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219983, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601219985, "dur": 36, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220023, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220025, "dur": 47, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220075, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220077, "dur": 31, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220110, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220112, "dur": 35, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220149, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220151, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220184, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220186, "dur": 35, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220224, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220226, "dur": 34, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220263, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220264, "dur": 36, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220302, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220304, "dur": 32, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220338, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220340, "dur": 36, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220379, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220381, "dur": 35, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220418, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220420, "dur": 34, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220457, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220459, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220496, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220498, "dur": 34, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220534, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220537, "dur": 34, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220573, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220575, "dur": 32, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220611, "dur": 36, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220649, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220650, "dur": 35, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220688, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220690, "dur": 35, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220727, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220729, "dur": 33, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220764, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220766, "dur": 34, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220802, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220803, "dur": 34, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220840, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220842, "dur": 35, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220879, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220881, "dur": 49, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220933, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220935, "dur": 31, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220969, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601220970, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221007, "dur": 32, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221041, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221043, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221080, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221083, "dur": 36, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221121, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221123, "dur": 35, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221160, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221162, "dur": 32, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221197, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221198, "dur": 33, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221235, "dur": 34, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221272, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221274, "dur": 35, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221311, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221313, "dur": 35, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221350, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221352, "dur": 36, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221390, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221392, "dur": 30, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221424, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221425, "dur": 35, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221462, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221464, "dur": 35, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221501, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221503, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221536, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221539, "dur": 36, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221577, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221579, "dur": 36, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221618, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221620, "dur": 34, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221656, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221657, "dur": 35, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221695, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221696, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221733, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221735, "dur": 36, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221774, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221776, "dur": 35, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221813, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221815, "dur": 36, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221853, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221855, "dur": 27, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221886, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221929, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221931, "dur": 35, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221969, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601221971, "dur": 38, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222011, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222013, "dur": 32, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222047, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222049, "dur": 31, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222084, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222085, "dur": 32, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222119, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222121, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222154, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222157, "dur": 256, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222417, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222455, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222458, "dur": 32, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222492, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222494, "dur": 34, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222530, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222532, "dur": 29, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222565, "dur": 106, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222675, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222714, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222716, "dur": 32, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222750, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222752, "dur": 39, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222793, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222795, "dur": 31, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222828, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222831, "dur": 56, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222890, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222893, "dur": 38, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222934, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222936, "dur": 35, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222974, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601222976, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223071, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223116, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223118, "dur": 44, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223164, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223167, "dur": 90, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223261, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223309, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223312, "dur": 40, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223354, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223357, "dur": 38, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223397, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223400, "dur": 105, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223511, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223573, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223576, "dur": 52, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223631, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223633, "dur": 108, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223744, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223746, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223794, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223796, "dur": 41, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223841, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223848, "dur": 35, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223886, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223889, "dur": 50, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223942, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223944, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223986, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601223988, "dur": 45, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224036, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224038, "dur": 33, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224074, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224076, "dur": 76, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224157, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224200, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224202, "dur": 45, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224250, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224252, "dur": 33, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224287, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224289, "dur": 79, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224374, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224417, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224420, "dur": 47, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224470, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224472, "dur": 31, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224507, "dur": 78, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224589, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224632, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224634, "dur": 45, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224682, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224684, "dur": 36, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224722, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224725, "dur": 96, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224825, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224869, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224872, "dur": 47, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224923, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601224926, "dur": 89, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225019, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225067, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225069, "dur": 39, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225112, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225114, "dur": 92, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225210, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225253, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225255, "dur": 46, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225303, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225305, "dur": 88, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225398, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225446, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225448, "dur": 35, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225487, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225536, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225538, "dur": 88, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225630, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225679, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225682, "dur": 42, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225727, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225729, "dur": 44, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225776, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225779, "dur": 40, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225821, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225824, "dur": 45, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225871, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225874, "dur": 40, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225917, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601225919, "dur": 95, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226019, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226061, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226064, "dur": 56, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226123, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226125, "dur": 102, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226232, "dur": 181, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226416, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226418, "dur": 45, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226466, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226468, "dur": 44, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226515, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226517, "dur": 75, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226595, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226598, "dur": 73, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226674, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226719, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226722, "dur": 45, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226770, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226772, "dur": 88, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226865, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226919, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226922, "dur": 51, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226976, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601226978, "dur": 47, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227027, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227030, "dur": 32, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227066, "dur": 80, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227151, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227191, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227192, "dur": 44, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227239, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227241, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227279, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227281, "dur": 94, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227379, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227421, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227423, "dur": 45, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227471, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227473, "dur": 33, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227509, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227511, "dur": 81, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227596, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227640, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227642, "dur": 45, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227690, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227692, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227727, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227729, "dur": 79, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227813, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227855, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227858, "dur": 40, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227901, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227903, "dur": 40, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227945, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227948, "dur": 46, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227997, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601227999, "dur": 39, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228041, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228043, "dur": 40, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228085, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228088, "dur": 34, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228125, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228126, "dur": 93, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228224, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228267, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228269, "dur": 45, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228316, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228318, "dur": 32, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228354, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228356, "dur": 78, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228438, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228481, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228483, "dur": 45, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228531, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228534, "dur": 35, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228571, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228573, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228642, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228683, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228684, "dur": 33, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228720, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228722, "dur": 32, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228757, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228758, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228846, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228887, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228889, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228925, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228927, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228964, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228965, "dur": 27, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601228996, "dur": 83, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229083, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229131, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229133, "dur": 41, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229177, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229179, "dur": 39, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229220, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229222, "dur": 70, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229297, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229345, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229347, "dur": 40, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229392, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229394, "dur": 37, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229434, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229436, "dur": 81, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229521, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229569, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229572, "dur": 40, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229614, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229617, "dur": 44, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229664, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229666, "dur": 33, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229702, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229704, "dur": 85, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229793, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229836, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229838, "dur": 45, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229885, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229887, "dur": 32, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229921, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601229923, "dur": 84, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230011, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230057, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230059, "dur": 46, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230108, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230110, "dur": 32, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230144, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230146, "dur": 79, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230229, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230269, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230272, "dur": 44, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230319, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230321, "dur": 88, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230413, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230462, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230465, "dur": 38, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230507, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230509, "dur": 102, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230615, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230658, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230660, "dur": 45, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230707, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230709, "dur": 33, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230745, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230747, "dur": 84, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230835, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230877, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230879, "dur": 48, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230929, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230931, "dur": 39, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230973, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601230975, "dur": 43, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231022, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231026, "dur": 45, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231073, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231075, "dur": 42, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231120, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231122, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231213, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231257, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231259, "dur": 110, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231375, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231409, "dur": 335, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231748, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231800, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231802, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231846, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231848, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231898, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231900, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231950, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231953, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231994, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601231996, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232033, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232036, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232073, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232075, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232113, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232115, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232158, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232161, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232201, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232204, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232238, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232240, "dur": 37, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232281, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232283, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232319, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232321, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232362, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232364, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232411, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232413, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232451, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232454, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232489, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232491, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232531, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232533, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232577, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232579, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232641, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232643, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232682, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232684, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232714, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232716, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232752, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232754, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232791, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232794, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232830, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232831, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232868, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232870, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232906, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232908, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232957, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601232960, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233003, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233006, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233045, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233047, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233090, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233092, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233139, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233142, "dur": 38, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233182, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233185, "dur": 43, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233233, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233236, "dur": 35, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233274, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233278, "dur": 36, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233316, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233319, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233375, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233378, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233426, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233429, "dur": 41, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233472, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233475, "dur": 36, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233513, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233516, "dur": 35, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233553, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233556, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233595, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233598, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233653, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233657, "dur": 55, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233715, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233719, "dur": 53, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233776, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233779, "dur": 51, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233835, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233839, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233885, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233888, "dur": 45, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233937, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233941, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233990, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601233993, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234040, "dur": 3, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234044, "dur": 43, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234090, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234093, "dur": 45, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234141, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234144, "dur": 53, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234201, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234204, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234259, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234262, "dur": 53, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234319, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234322, "dur": 49, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234375, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234378, "dur": 48, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234431, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234434, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234479, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234482, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234526, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234529, "dur": 34, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234566, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234568, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234606, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234609, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234645, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234647, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234686, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234688, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234733, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234736, "dur": 42, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234781, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234783, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234820, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234822, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234860, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234862, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234903, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234939, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601234942, "dur": 5945, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601240893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601240896, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601240946, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601240948, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241078, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241129, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241170, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241171, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241252, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241291, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241293, "dur": 591, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241888, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241890, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241930, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241968, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601241970, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242006, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242007, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242099, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242160, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242447, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242488, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242491, "dur": 230, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242726, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242763, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242765, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242806, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242809, "dur": 86, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242900, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242933, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601242935, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243056, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243091, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243093, "dur": 365, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243462, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243498, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243500, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243543, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243582, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243584, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243625, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243628, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243669, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243671, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243728, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243773, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243777, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243816, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243819, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243861, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243864, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243909, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243945, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243947, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243981, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601243983, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244035, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244073, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244075, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244113, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244116, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244149, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244151, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244252, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244288, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244290, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244323, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244324, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244364, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244399, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244401, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244471, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244511, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244514, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244552, "dur": 308, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244866, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244901, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244903, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601244938, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245008, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245043, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245045, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245080, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245122, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245125, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245162, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245164, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245216, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245253, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245255, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245302, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245337, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245339, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245378, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245380, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245416, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245418, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245462, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245464, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245532, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245566, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245568, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245664, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245701, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245703, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245738, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245740, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245781, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245784, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245819, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601245821, "dur": 225, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246051, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246086, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246088, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246130, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246226, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246267, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246269, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246319, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246321, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246365, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246416, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246454, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246456, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246507, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246510, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246556, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246559, "dur": 181, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246745, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246831, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601246834, "dur": 290, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247128, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247207, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247208, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247254, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247304, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247346, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247348, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247391, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247394, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601247494, "dur": 544, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248041, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248096, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248099, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248214, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248257, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248260, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248305, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248432, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601248479, "dur": 685, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601249168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601249170, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601249220, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601249224, "dur": 68461, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601317694, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601317697, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601317745, "dur": 1444, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601319193, "dur": 10208, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329410, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329414, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329465, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329467, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329656, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329705, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329708, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329758, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329760, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329832, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329864, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601329952, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330009, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330011, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330086, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330125, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330127, "dur": 612, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330745, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330782, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601330784, "dur": 1401, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332192, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332219, "dur": 403, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332627, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332661, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332664, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332697, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332699, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332730, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332814, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601332846, "dur": 530, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601333380, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601333416, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601333520, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601333553, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601333894, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601333925, "dur": 1314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335242, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335277, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335402, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335445, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335447, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335488, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335521, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335523, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335765, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335807, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335809, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335856, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335859, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335902, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335904, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335947, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335991, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601335993, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336035, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336038, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336075, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336077, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336121, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336123, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336160, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336163, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336231, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336270, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336272, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336311, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336313, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336352, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336355, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336389, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336391, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336427, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336429, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336469, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336472, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336511, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336513, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336551, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336553, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336601, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336644, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336647, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336686, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336688, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336727, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336729, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336764, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336767, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336803, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336806, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336844, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336846, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336882, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336884, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336923, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336925, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336960, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601336962, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337003, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337035, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337037, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337122, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337124, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337183, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337229, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337261, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337264, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337297, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337299, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337333, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337428, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337430, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337469, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337471, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337618, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337668, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337671, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337718, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337722, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337765, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337835, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337883, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601337886, "dur": 263, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338152, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338153, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338201, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338203, "dur": 396, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338604, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338661, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338664, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338718, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338721, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338774, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338776, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338813, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601338815, "dur": 64551, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601403374, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601403378, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601403437, "dur": 19, "ph": "X", "name": "ProcessMessages 1076", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601403458, "dur": 40373, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601443839, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601443842, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601443880, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601443884, "dur": 70882, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601514775, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601514779, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601514883, "dur": 18, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601514903, "dur": 26898, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601541810, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601541813, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601541900, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601541904, "dur": 1745, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601543657, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601543660, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601543715, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601543741, "dur": 5769, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601549519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601549522, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601549630, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601549634, "dur": 628, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550272, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550330, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550357, "dur": 557, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550919, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550921, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601550968, "dur": 405, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 31688, "tid": 12884901888, "ts": 1755111601551377, "dur": 7358, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 31688, "tid": 92, "ts": 1755111601576467, "dur": 2024, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 31688, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 31688, "tid": 8589934592, "ts": 1755111601191657, "dur": 74196, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 31688, "tid": 8589934592, "ts": 1755111601265856, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 31688, "tid": 8589934592, "ts": 1755111601265861, "dur": 1469, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 31688, "tid": 92, "ts": 1755111601578495, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 31688, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 31688, "tid": 4294967296, "ts": 1755111601170324, "dur": 390376, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 31688, "tid": 4294967296, "ts": 1755111601175676, "dur": 8954, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 31688, "tid": 4294967296, "ts": 1755111601560908, "dur": 5826, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 31688, "tid": 4294967296, "ts": 1755111601564719, "dur": 170, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 31688, "tid": 4294967296, "ts": 1755111601566848, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 31688, "tid": 92, "ts": 1755111601578503, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755111601203705, "dur": 51, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601203792, "dur": 2780, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601206582, "dur": 819, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601207578, "dur": 73, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755111601207651, "dur": 614, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601209660, "dur": 361, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111601211280, "dur": 1734, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_6CCCC88903086FA7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111601213074, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755111601213542, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111601214111, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1755111601214419, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755111601217435, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755111601217644, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755111601218375, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111601208302, "dur": 23234, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601231549, "dur": 319047, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601550597, "dur": 304, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601551084, "dur": 81, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601551195, "dur": 1127, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755111601208979, "dur": 22779, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601231759, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_AF66D8329BCEF71E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601232432, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9AB55CF056145574.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601232573, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_A7499BA3B50749DD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601232645, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_32939C22C21A9A23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601232735, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B3100D568D19DE53.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601232806, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_CCD38C9CCE3A7CB2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601232947, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601233060, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BC1462186899290E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601233154, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_22355095CF1A36B0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601233331, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601233549, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601233734, "dur": 7614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601241439, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601241568, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601242282, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601242389, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601242798, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601243057, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601243901, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601244183, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601244252, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601244558, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601245437, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601245681, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601246122, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601246741, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601246846, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111601247050, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601247642, "dur": 79463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601327108, "dur": 2842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601330003, "dur": 2894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601332939, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111601336974, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601337077, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601337242, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1755111601337534, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601337599, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601338001, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111601338963, "dur": 211627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601208318, "dur": 23244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601231595, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601231677, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_3497E18D9ACE6C53.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232184, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232319, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_87AA2ADA08DFF7E1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232405, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_87AA2ADA08DFF7E1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232468, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_810264E72E8EDE2D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232652, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_810264E72E8EDE2D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232738, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FC3813CDB6AE67C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601232983, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_0BFC51EF888A92A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601233076, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_88B40F493262C216.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601233296, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601233379, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601233492, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111601233660, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1755111601234066, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111601234251, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755111601234396, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111601234623, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111601234858, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111601234913, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601234999, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111601235142, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601236332, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601237134, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601237394, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601237721, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601237980, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601238253, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601238743, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601239042, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601239308, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601240108, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601240390, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601241286, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601241424, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601242256, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601242815, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601243062, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601243795, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601244092, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601244368, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601245285, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601245417, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601245685, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601245743, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601246028, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601246664, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601246773, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601246861, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111601247019, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601247420, "dur": 79688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601327110, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601329982, "dur": 3810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601333839, "dur": 4250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755111601338163, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111601339078, "dur": 211523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601208371, "dur": 23211, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601231595, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601231680, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3B4D13D712F00EB8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601232228, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_12994AB43863D35B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601232290, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601232411, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A42BD3F60EFF66D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601232615, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7C053C2C91326FF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601232720, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_31B99CCAA2C60608.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601232798, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601233051, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601233162, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_8A77BBF0F2259C97.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601233246, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_8A77BBF0F2259C97.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601233533, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755111601233829, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601234374, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755111601234487, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111601234670, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755111601234840, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111601234913, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2210688342310731613.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111601235087, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1000725841676588325.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111601235192, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601236312, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601237128, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601237419, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601237738, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601238004, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601238301, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601238643, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601239015, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601239286, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601239987, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601240275, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601240524, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601240782, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601241161, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601241425, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601242228, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601242805, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601243080, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755111601243864, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601243958, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601244044, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111601244351, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601244428, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755111601245212, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601245321, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755111601246294, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601246403, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601246777, "dur": 20864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601269859, "dur": 450, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1755111601270310, "dur": 2000, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 3, "ts": 1755111601272311, "dur": 125, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 3, "ts": 1755111601267642, "dur": 4801, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601272444, "dur": 54684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601327145, "dur": 2718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111601329864, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601330394, "dur": 2688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111601333128, "dur": 4572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111601337701, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601337946, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601338036, "dur": 987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111601339052, "dur": 211556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601208409, "dur": 23183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601231602, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_4D4853D2BE4AE8AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232110, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_22C73FD68F7437D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232199, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_E221DA127803E46F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232348, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0CAABF378ACEABEF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232413, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0CAABF378ACEABEF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232599, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7FE145A456DFCBEF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232724, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_91BF7E65A37C8C47.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232795, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601232931, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601233049, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_FB367C9346FF9A5D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601233150, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_AE7BD4FEA5F1A611.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601233285, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601233378, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F327315904C52B00.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601233469, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601233675, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111601233876, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111601234116, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111601234537, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111601234669, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1755111601234778, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111601234877, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601235036, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601235434, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601236595, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601236782, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601237017, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601237414, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601237749, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601238017, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601238763, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601239135, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601239387, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601240109, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601240405, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601241191, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601241443, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601242254, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601243277, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601244148, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601244268, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601244574, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601244785, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601245553, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601245741, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601246010, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601246770, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601246856, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601247015, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601247702, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601247892, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601248487, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601248568, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111601248713, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601249398, "dur": 58, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601250029, "dur": 153592, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601410321, "dur": 30626, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1755111601410308, "dur": 32100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601443806, "dur": 231, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111601444396, "dur": 70652, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755111601541884, "dur": 7797, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1755111601541873, "dur": 7810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1755111601549705, "dur": 838, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1755111601208453, "dur": 23154, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601231618, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_B430B0CD9867C792.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601232275, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_EF946CA7674E0325.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601232492, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601232604, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2D8413BDE13CD7F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601232696, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2D8413BDE13CD7F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601232792, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601232978, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3D6AFCA43E0B6878.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601233097, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6126AAA2007E3BC1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601233690, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_BC4BCFD1CE67FD5E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601233837, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1755111601234004, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1755111601234211, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1755111601234467, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111601234551, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111601234906, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111601235046, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601236086, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601236998, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601237261, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601237533, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601237905, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601238164, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601238418, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601238973, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601239242, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601239973, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601240244, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601240496, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601240762, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601241097, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601241460, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601242226, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601242804, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601243084, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755111601243918, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_011CCB039EE5F887.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601244038, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ParrelSync.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111601244348, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ParrelSync.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755111601245237, "dur": 1482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755111601246720, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601246838, "dur": 80275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601327114, "dur": 2977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755111601330092, "dur": 952, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601331056, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755111601333712, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601334208, "dur": 3189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755111601337398, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601337613, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601338006, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111601338983, "dur": 211600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601208495, "dur": 23124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601231629, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B147A254CFD2102C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232161, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D8824977E7F2B23E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232218, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601232279, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_29BCCE2A849A4ABC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232355, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CE9F463A1284211A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232500, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5AAA2FC2CAC01A77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232643, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8E87AC3CC97FF6E8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232716, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_58C2D2C6FDB49C32.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601232837, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601232954, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_403651568F253731.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601233009, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601233268, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601233382, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601233572, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601233763, "dur": 7361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601241204, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601241452, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601242251, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601242825, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601243077, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601243914, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601244001, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601244110, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601244416, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601244681, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601245433, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111601245696, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601246559, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601246627, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601246810, "dur": 80301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601327112, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601330070, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601330143, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601332893, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111601333002, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601335797, "dur": 3113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111601338973, "dur": 211613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601208602, "dur": 23031, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601231642, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B428977015ACB395.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232142, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2E8623D16E8E668C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232263, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232370, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232433, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0BCE78C25C474DFA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232519, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_3787BA530AC70137.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232585, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_3787BA530AC70137.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232812, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_8F866AA12C4C5E35.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601232977, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_8F866AA12C4C5E35.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601233040, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_EDF98A79C83CDC6C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601233221, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_81542F95A0721D6A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601233311, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_81542F95A0721D6A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601233420, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CA09514599DD5F0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601233587, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601233805, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111601233907, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755111601234290, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1755111601234617, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111601234889, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111601235079, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601236159, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601237200, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601237466, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601237793, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601238040, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601238302, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601238654, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601239018, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601239298, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601239994, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601240309, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601241275, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601241440, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601242234, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601242812, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601243094, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601243994, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601244163, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601244326, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601244610, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601245334, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601245397, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601246559, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111601246749, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601247332, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601247516, "dur": 79607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601327131, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601329923, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601329997, "dur": 2830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601332828, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601332965, "dur": 2647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601335613, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111601335715, "dur": 3140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111601338930, "dur": 211680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601208661, "dur": 22988, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601231659, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1A6A88D66EF4C557.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232334, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A3DF7B62ADBC8E51.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232407, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A3DF7B62ADBC8E51.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232487, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F12DCDB48BAEBA17.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232580, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232647, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232705, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EFF9361EDEBC0A6E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232774, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EFF9361EDEBC0A6E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232859, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_C763F25127858F24.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601232992, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601233302, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601233393, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601233466, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_6CCCC88903086FA7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601233537, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_6CCCC88903086FA7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601234005, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755111601234154, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601234205, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111601234287, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755111601234404, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111601234531, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111601234941, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111601235005, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111601235059, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601236226, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601237055, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601237316, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601237604, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601237917, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601238202, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601238462, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601239056, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601239321, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601240061, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601240428, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601241232, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601241479, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601242239, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601242855, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601243214, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601244062, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111601245169, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111601245528, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111601245846, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111601246520, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601246793, "dur": 80350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601327146, "dur": 2746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755111601329893, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601330024, "dur": 7780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755111601337805, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601337930, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601337994, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111601338486, "dur": 212101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601208710, "dur": 22952, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601231671, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232229, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B3BE1B8334830D52.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232323, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E0C67EE87749D19A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232391, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E0C67EE87749D19A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232450, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_334958EB079AC540.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232524, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_334958EB079AC540.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232638, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2AE61BEFE92D422E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232732, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BBF013107B6E7683.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601232880, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_84C61B5FFF6AE5F9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601233085, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F03F48A1E46251D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601233173, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F03F48A1E46251D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601233666, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1755111601233905, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1755111601234104, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755111601234286, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755111601234443, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755111601234667, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1755111601234862, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755111601235007, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2499735490941455596.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755111601235082, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601236275, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601237216, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601237510, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601237808, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601238085, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601238350, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601238752, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601239039, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601239288, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601239995, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601240310, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601241132, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601241459, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601242255, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601242810, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601243049, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601243367, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111601244035, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601244254, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111601244583, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111601246045, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601246118, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601246778, "dur": 25671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601272450, "dur": 54665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111601327119, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111601329685, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111601332511, "dur": 2994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ParrelSync.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111601335554, "dur": 3431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111601339055, "dur": 211556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601208795, "dur": 22889, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601231693, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232222, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601232316, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232393, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_52799C164F48C7C2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232469, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_8824D3AD2222F26E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232693, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232751, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5267BC5A847D8B9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232835, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C3A5EBA3221A6287.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601232991, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_441A482B34BB1A1B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601233093, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_14BBB0A1A56B54F9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601233192, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601233610, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601234002, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111601234068, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111601234291, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755111601234440, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111601234766, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111601234903, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111601235043, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601236136, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601236984, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601237247, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601237535, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601238037, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601238302, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601238749, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601239073, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601239345, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601240108, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601240392, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601241215, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601241456, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601242247, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601242797, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601243017, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601243103, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601244292, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601244414, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601244667, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601244807, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601245727, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111601245973, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601246631, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601246749, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1755111601247326, "dur": 126, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601247756, "dur": 70223, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1755111601327120, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601329965, "dur": 2749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601332715, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601332997, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601335652, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111601335717, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111601338467, "dur": 212117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601208865, "dur": 22831, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601231705, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_409E75408550DFD9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232311, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_00FC4C225939AAE2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232377, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_00FC4C225939AAE2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232543, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_C81CFFEB4B719421.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232614, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232699, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232833, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_284B36A6DA56D348.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601232956, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601233090, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_70F846A33051C353.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601233192, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_94EF8D6143DA44EF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601233286, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601233537, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755111601233852, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1755111601234006, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1755111601234214, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755111601234886, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1755111601235104, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601236339, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601237094, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601237346, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601237620, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601237942, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601238241, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601238494, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601238906, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601239170, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601239453, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601240142, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601240443, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601240696, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601241074, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601241453, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601242233, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601242802, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601243036, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601243768, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601243855, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601243938, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601244177, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601244842, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601245537, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601245772, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601246074, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601246774, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601247710, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111601247899, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601248547, "dur": 78601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601327150, "dur": 3073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601330266, "dur": 3364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601333631, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601333695, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755111601336469, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601336647, "dur": 404, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601337321, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601337392, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601337732, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601338179, "dur": 203731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111601541936, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1755111601541912, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1755111601542066, "dur": 1876, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1755111601543949, "dur": 6661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601208964, "dur": 22775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601231747, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DF6F7FF917E347B3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232340, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_66BBE97858D0953F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232407, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_66BBE97858D0953F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232503, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1FFECC68464BBF36.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232583, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232671, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C7ED8541370EC3E2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232832, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E714CD2BFF109E7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601232991, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E714CD2BFF109E7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601233189, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2B2A94CF86E88A71.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601233279, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601233535, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1755111601233733, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601233948, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1755111601234730, "dur": 492, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111601235223, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601236294, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601237298, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601237588, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601237910, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601238172, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601238451, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601238888, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601239152, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601239418, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601240122, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601240436, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601240845, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601241079, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601241426, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601242239, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601242846, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601243088, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601243936, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601244158, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601244424, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601244680, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601245405, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_8F79B00F05E033D6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601245613, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601245760, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111601246033, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601246722, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601246827, "dur": 80298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601327126, "dur": 2807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601329934, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601329993, "dur": 2978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601333015, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601335634, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111601335722, "dur": 3246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111601339064, "dur": 211525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111601556993, "dur": 1459, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 31688, "tid": 92, "ts": 1755111601579245, "dur": 2370, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 31688, "tid": 92, "ts": 1755111601581662, "dur": 2542, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 31688, "tid": 92, "ts": 1755111601573786, "dur": 12435, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}