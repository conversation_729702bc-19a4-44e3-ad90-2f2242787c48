using UnityEngine;

public class PlayerController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    [Header("Movement")]
    public float moveSpeed = 5f;

    [<PERSON>er("Network")]
    public bool isLocalPlayer = true; // Chỉ local player mới nhận input

    private Vector3 _lastPosition;

    private void Start()
    {
        _lastPosition = transform.position;

        // Chỉ local player mới gửi position
        if (isLocalPlayer)
        {
            InvokeRepeating(nameof(SendPosition), 0f, 0.1f);
        }
    }

    private void FixedUpdate()
    {
        // Chỉ local player mới nhận input
        if (isLocalPlayer)
        {
            HandleMovement();
        }
    }

    private void HandleMovement()
    {
        float x = Input.GetAxisRaw("Horizontal");
        float y = Input.GetAxisRaw("Vertical");

        Vector3 movement = new Vector3(x, y, 0) * moveSpeed * Time.deltaTime;
        transform.position += movement;
    }

    private void SendPosition()
    {
        // Chỉ local player mới gửi position
        if (!isLocalPlayer) return;

        if (Vector3.Distance(transform.position, _lastPosition) > 0.01f)
        {
            string playerId = NetworkManager.Instance.GetMyPlayerId();
            Vector3 pos = transform.position;
            string message = $"POSITION:{playerId}:{pos.x:F2},{pos.y:F2},{pos.z:F2}";

            NetworkManager.Instance.SendMessage(message);
            _lastPosition = transform.position;
        }
    }
}