using UnityEngine;

public class PlayerController : <PERSON>o<PERSON>eh<PERSON><PERSON>
{
    [Header("Movement")]
    public float moveSpeed = 5f;
    
    private Vector3 _lastPosition;
    
    private void Start()
    {
        _lastPosition = transform.position;
        InvokeRepeating(nameof(SendPosition), 0f, 0.1f);
    }
    
    private void FixedUpdate()
    {
        HandleMovement();
    }
    
    private void HandleMovement()
    {
        float x = Input.GetAxisRaw("Horizontal");
        float z = Input.GetAxisRaw("Vertical");
        
        Vector3 movement = new Vector3(x, 0, z) * moveSpeed * Time.deltaTime;
        transform.position += movement;
    }
    
    private void SendPosition()
    {
        if (Vector3.Distance(transform.position, _lastPosition) > 0.01f)
        {
            string playerId = NetworkManager.Instance.GetMyPlayerId();
            Vector3 pos = transform.position;
            string message = $"POSITION:{playerId}:{pos.x:F2},{pos.y:F2},{pos.z:F2}";
            
            NetworkManager.Instance.SendMessage(message);
            _lastPosition = transform.position;
        }
    }
}