﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Packages/com.unity.collections/Unity.Collections/NativeText.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;
using Unity.Burst;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using UnityEngine.Internal;
using Unity.Properties;



namespace Unity.Collections
{
    /// <summary>
    /// An unmanaged, mutable, resizable UTF-8 string.
    /// </summary>
    /// <remarks>
    /// The string is always null-terminated, meaning a zero byte always immediately follows the last character.
    /// </remarks>
    [StructLayout(LayoutKind.Sequential)]
    [NativeContainer]
    [DebuggerDisplay("Length = {Length}")]
    [GenerateTestsForBurstCompatibility]
    public unsafe partial struct NativeText
        : INativeList<byte>
        , INativeDisposable
        , IUTF8Bytes
        , IComparable<String>
        , IEquatable<String>
        , IComparable<NativeText>
        , IEquatable<NativeText>
        , IComparable<FixedString32Bytes>
        , IEquatable<FixedString32Bytes>
        , IComparable<FixedString64Bytes>
        , IEquatable<FixedString64Bytes>
        , IComparable<FixedString128Bytes>
        , IEquatable<FixedString128Bytes>
        , IComparable<FixedString512Bytes>
        , IEquatable<FixedString512Bytes>
        , IComparable<FixedString4096Bytes>
        , IEquatable<FixedString4096Bytes>
    {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
        internal AtomicSafetyHandle m_Safety;
        internal static readonly SharedStatic<int> s_staticSafetyId = SharedStatic<int>.GetOrCreate<NativeText>();
#endif
        // NOTE! This Length is always > 0, because we have a null terminating byte.
        // We hide this byte from NativeText users.
        [NativeDisableUnsafePtrRestriction]
        internal UnsafeText* m_Data;

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        [ExcludeFromBurstCompatTesting("Takes managed string")]
        public NativeText(String source, Allocator allocator) : this(source, (AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        [ExcludeFromBurstCompatTesting("Takes managed string")]
        public NativeText(String source, AllocatorManager.AllocatorHandle allocator) : this(source.Length * 2, allocator)
        {
            Length = source.Length * 2;
            unsafe
            {
                fixed (char* sourceptr = source)
                {
                    var error = UTF8ArrayUnsafeUtility.Copy(GetUnsafePtr(), out var actualBytes, Capacity, sourceptr, source.Length);
                    if (error != CopyError.None)
                    {
                        CheckNull(m_Data);
                        m_Data->Dispose();
                        m_Data = UnsafeText.Alloc(allocator);
                        *m_Data = default(UnsafeText);
                        ThrowCopyError(error, source);
                    }
                    Length = actualBytes;
                }
            }

#if ENABLE_UNITY_COLLECTIONS_CHECKS
            CollectionHelper.SetStaticSafetyId(ref m_Safety, ref s_staticSafetyId.Data, "Unity.Collections.NativeText");

            AtomicSafetyHandle.SetBumpSecondaryVersionOnScheduleWrite(m_Safety, true);
#endif
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText.
        /// </summary>
        /// <param name="capacity">The initial capacity in bytes.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(int capacity, Allocator allocator) : this(capacity, (AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText.
        /// </summary>
        /// <param name="capacity">The initial capacity in bytes.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(int capacity, AllocatorManager.AllocatorHandle allocator)
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            CollectionHelper.CheckAllocator(allocator);

            m_Safety = CollectionHelper.CreateSafetyHandle(allocator);

            CollectionHelper.SetStaticSafetyId(ref m_Safety, ref s_staticSafetyId.Data, "Unity.Collections.NativeText");

            AtomicSafetyHandle.SetBumpSecondaryVersionOnScheduleWrite(m_Safety, true);
#endif
            m_Data = UnsafeText.Alloc(allocator);
            *m_Data = new UnsafeText(capacity, allocator);
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with an initial capacity of 512 bytes.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(Allocator allocator) : this((AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with an initial capacity of 512 bytes.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(AllocatorManager.AllocatorHandle allocator) : this(512, allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString32Bytes source, AllocatorManager.AllocatorHandle allocator)
            : this(source.utf8LengthInBytes, allocator)
        {
            Length = source.utf8LengthInBytes;
            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data->GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString32Bytes source, Allocator allocator)
            : this(source, (AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString64Bytes source, AllocatorManager.AllocatorHandle allocator)
            : this(source.utf8LengthInBytes, allocator)
        {
            Length = source.utf8LengthInBytes;
            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data->GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString64Bytes source, Allocator allocator)
            : this(source, (AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString128Bytes source, AllocatorManager.AllocatorHandle allocator)
            : this(source.utf8LengthInBytes, allocator)
        {
            Length = source.utf8LengthInBytes;
            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data->GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString128Bytes source, Allocator allocator)
            : this(source, (AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString512Bytes source, AllocatorManager.AllocatorHandle allocator)
            : this(source.utf8LengthInBytes, allocator)
        {
            Length = source.utf8LengthInBytes;
            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data->GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString512Bytes source, Allocator allocator)
            : this(source, (AllocatorManager.AllocatorHandle)allocator)
        {
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString4096Bytes source, AllocatorManager.AllocatorHandle allocator)
            : this(source.utf8LengthInBytes, allocator)
        {
            Length = source.utf8LengthInBytes;
            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data->GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Initializes and returns an instance of NativeText with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public NativeText(in FixedString4096Bytes source, Allocator allocator)
            : this(source, (AllocatorManager.AllocatorHandle)allocator)
        {
        }


        /// <summary>
        /// The current length in bytes of this string.
        /// </summary>
        /// <remarks>
        /// The length does not include the null terminator byte.
        /// </remarks>
        /// <value>The current length in bytes of the UTF-8 encoded string.</value>
        public int Length
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            readonly get
            {
                CheckNull(m_Data);
                CheckRead();
                return m_Data->Length;
            }

            set
            {
                CheckNull(m_Data);
                CheckWriteAndBumpSecondaryVersion();
                m_Data->Length = value;
            }
        }

        /// <summary>
        /// The current capacity in bytes of this string.
        /// </summary>
        /// <remarks>
        /// The null-terminator byte is not included in the capacity, so the string's character buffer is `Capacity + 1` in size.
        /// </remarks>
        /// <value>The current capacity in bytes of the string.</value>
        public int Capacity
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            readonly get
            {
                CheckNull(m_Data);
                CheckRead();
                return m_Data->Capacity;
            }

            set
            {
                CheckNull(m_Data);
                CheckWriteAndBumpSecondaryVersion();
                m_Data->Capacity = value;
            }
        }

        /// <summary>
        /// Attempt to set the length in bytes of this string.
        /// </summary>
        /// <param name="newLength">The new length in bytes of the string.</param>
        /// <param name="clearOptions">Whether any bytes added should be zeroed out.</param>
        /// <returns>Always true.</returns>
        public bool TryResize(int newLength, NativeArrayOptions clearOptions = NativeArrayOptions.ClearMemory)
        {
            CheckWrite();

            // this can't ever fail, because if we can't resize malloc will abort
            Length = newLength;
            return true;
        }

        /// <summary>
        /// Whether this string has no characters.
        /// </summary>
        /// <value>True if this string has no characters or the string has not been constructed.</value>
        /// <exception cref="NotSupportedException">Thrown if ENABLE_UNITY_COLLECTIONS_CHECKS is defined and a write is attempted.</exception>
        public readonly bool IsEmpty
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                if (!IsCreated)
                {
                    return true;
                }

                CheckRead();
                return m_Data->IsEmpty;
            }
        }

        /// <summary>
        /// Whether this string's character buffer has been allocated (and not yet deallocated).
        /// </summary>
        /// <value>Whether this string's character buffer has been allocated (and not yet deallocated).</value>
        public readonly bool IsCreated
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get => m_Data != null;
        }

        /// <summary>
        /// Returns a pointer to this string's character buffer.
        /// </summary>
        /// <remarks>
        /// The pointer is made invalid by operations that reallocate the character buffer, such as setting <see cref="Capacity"/>.
        /// </remarks>
        /// <returns>A pointer to this string's character buffer.</returns>
        public unsafe byte* GetUnsafePtr()
        {
            CheckNull(m_Data);
            CheckRead();
            return m_Data->GetUnsafePtr();
        }

        /// <summary>
        /// The byte at an index. Note that writing to a NativeText.Readonly is not supported; the setter of this property throws when safety checks are enabled.
        /// </summary>
        /// <param name="index">A zero-based byte index.</param>
        /// <value>The byte at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public byte this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                CheckNull(m_Data);
                CheckRead();
                return m_Data->ElementAt(index);
            }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set
            {
                CheckNull(m_Data);
                CheckWrite();
                m_Data->ElementAt(index) = value;
            }
        }

        /// <summary>
        /// Returns a reference to the byte (not character) at an index.
        /// </summary>
        /// <remarks>
        /// Deallocating or reallocating this string's character buffer makes the reference invalid.
        /// </remarks>
        /// <param name="index">A byte index.</param>
        /// <returns>A reference to the byte at the index.</returns>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public ref byte ElementAt(int index)
        {
            CheckNull(m_Data);
            CheckWrite();
            return ref m_Data->ElementAt(index);
        }

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Appends a byte.
        /// </summary>
        /// <remarks>
        /// A zero byte will always follow the newly appended byte.
        ///
        /// No validation is performed: it is your responsibility for the bytes of the string to form valid UTF-8 when you're done appending bytes.
        /// </remarks>
        /// <param name="value">A byte to append.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Add(in byte value)
        {
            CheckWrite();
            this[Length++] = value;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(NativeText other)
        {
            CheckRead();
            return FixedStringMethods.CompareTo(ref this, *other.m_Data);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(NativeText other)
        {
            CheckRead();
            return FixedStringMethods.Equals(ref this, *other.m_Data);
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(NativeText.ReadOnly other)
        {
            CheckRead();
            return FixedStringMethods.CompareTo(ref this, other);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(NativeText.ReadOnly other)
        {
            CheckRead();
            return FixedStringMethods.Equals(ref this, *other.m_Data);
        }

        /// <summary>
        /// Releases all resources (memory and safety handles).
        /// </summary>
        public void Dispose()
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            if (!AtomicSafetyHandle.IsDefaultValue(m_Safety))
            {
                AtomicSafetyHandle.CheckExistsAndThrow(m_Safety);
            }
#endif
            if (!IsCreated)
            {
                return;
            }

#if ENABLE_UNITY_COLLECTIONS_CHECKS
            CollectionHelper.DisposeSafetyHandle(ref m_Safety);
#endif
            UnsafeText.Free(m_Data);
            m_Data = null;
        }

        /// <summary>
        /// Creates and schedules a job that releases all resources (memory and safety handles) of this NativeText.
        /// </summary>
        /// <param name="inputDeps">The dependency for the new job.</param>
        /// <returns>The handle of the new job. The job depends upon `inputDeps` and releases all resources (memory and safety handles) of this NativeText.</returns>
        public JobHandle Dispose(JobHandle inputDeps)
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            if (!AtomicSafetyHandle.IsDefaultValue(m_Safety))
            {
                AtomicSafetyHandle.CheckExistsAndThrow(m_Safety);
            }
#endif
            if (!IsCreated)
            {
                return inputDeps;
            }

#if ENABLE_UNITY_COLLECTIONS_CHECKS
            var jobHandle = new NativeTextDisposeJob { Data = new NativeTextDispose { m_TextData = m_Data, m_Safety = m_Safety } }.Schedule(inputDeps);
            AtomicSafetyHandle.Release(m_Safety);
#else
            var jobHandle = new NativeTextDisposeJob { Data = new NativeTextDispose { m_TextData = m_Data } }.Schedule(inputDeps);
#endif
            m_Data = null;

            return jobHandle;
        }

        /// <summary>
        /// A copy of this string as a managed string.
        /// </summary>
        /// <remarks>
        /// For internal use only. Use <see cref="ToString"/> instead.
        /// </remarks>
        /// <value>A copy of this string as a managed string.</value>
        [CreateProperty]
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        [ExcludeFromBurstCompatTesting("Returns managed string")]
        public string Value => ToString();

        /// <summary>
        /// An enumerator over the characters (not bytes) of a NativeText.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, its index is invalid. The first <see cref="MoveNext"/> call advances the enumerator's index to the first character.
        /// </remarks>
        public struct Enumerator : IEnumerator<Unicode.Rune>
        {
            NativeText.ReadOnly target;
            int offset;
            Unicode.Rune current;

            /// <summary>
            /// Initializes and returns an instance of NativeText.Enumerator.
            /// </summary>
            /// <param name="source">A NativeText for which to create an enumerator.</param>
            public Enumerator(NativeText source)
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                AtomicSafetyHandle.CheckGetSecondaryDataPointerAndThrow(source.m_Safety);

                var ash = source.m_Safety;
                AtomicSafetyHandle.UseSecondaryVersion(ref ash);

                target = new ReadOnly(source.m_Data, ash);
#else
                target = source.AsReadOnly();
#endif
                offset = 0;
                current = default;
            }


            /// <summary>
            /// Initializes and returns an instance of NativeText.Enumerator.
            /// </summary>
            /// <param name="source">A NativeText.ReadOnly for which to create an enumerator.</param>
            public Enumerator(NativeText.ReadOnly source)
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                AtomicSafetyHandle.CheckGetSecondaryDataPointerAndThrow(source.m_Safety);

                var ash = source.m_Safety;
                AtomicSafetyHandle.UseSecondaryVersion(ref ash);

                target = new ReadOnly(source.m_Data, ash);
#else
                target = source;
#endif
                offset = 0;
                current = default;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next character, returning true if <see cref="Current"/> is valid to read afterwards.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            public bool MoveNext()
            {
                if (offset >= target.Length)
                    return false;

                unsafe
                {
                    Unicode.Utf8ToUcs(out current, target.GetUnsafePtr(), ref offset, target.Length);
                }

                return true;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                offset = 0;
                current = default;
            }

            object IEnumerator.Current
            {
                [MethodImpl(MethodImplOptions.AggressiveInlining)]
                get => Current;
            }

            /// <summary>
            /// The current character.
            /// </summary>
            /// <value>The current character.</value>
            public Unicode.Rune Current => current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the characters of the NativeText.
        /// </summary>
        /// <returns>An enumerator for iterating over the characters of the NativeText.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(this);
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        [ExcludeFromBurstCompatTesting("Takes managed string")]
        public int CompareTo(String other)
        {
            return ToString().CompareTo(other);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        [ExcludeFromBurstCompatTesting("Takes managed string")]
        public bool Equals(String other)
        {
            return ToString().Equals(other);
        }



        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString32Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in NativeText a, in FixedString32Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in NativeText a, in FixedString32Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString32Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString64Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in NativeText a, in FixedString64Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in NativeText a, in FixedString64Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString64Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString128Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in NativeText a, in FixedString128Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in NativeText a, in FixedString128Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString128Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString512Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in NativeText a, in FixedString512Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in NativeText a, in FixedString512Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString512Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString4096Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in NativeText a, in FixedString4096Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in NativeText a, in FixedString4096Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString4096Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns a managed string copy of this string.
        /// </summary>
        /// <returns>A managed string copy of this string.</returns>
        [ExcludeFromBurstCompatTesting("Returns managed string")]
        public override String ToString()
        {
            if (m_Data == null)
                return "";
            CheckRead();
            return this.ConvertToString();
        }

        /// <summary>
        /// Returns a hash code of this string.
        /// </summary>
        /// <remarks>The hash code is an integer that is always the same for two equal strings but (very likely) different for two unequal strings.</remarks>
        /// <returns>A hash code of this string.</returns>
        public override int GetHashCode()
        {
            return this.ComputeHashCode();
        }

        /// <summary>
        /// Returns true if this string and another object are equal.
        /// </summary>
        /// <remarks>For the object to be equal, it must itself be a managed string, NativeText, or FixedString*N*Bytes.
        ///
        /// Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if this string and the object are equal.</returns>
        [ExcludeFromBurstCompatTesting("Takes managed object")]
        public override bool Equals(object other)
        {
            if(ReferenceEquals(null, other)) return false;
            if(other is String aString) return Equals(aString);
            if(other is NativeText aNativeText) return Equals(aNativeText);
            if(other is ReadOnly aReadOnly) return Equals(aReadOnly);
            if(other is FixedString32Bytes a32) return Equals(a32);
            if(other is FixedString64Bytes a64) return Equals(a64);
            if(other is FixedString128Bytes a128) return Equals(a128);
            if(other is FixedString512Bytes a512) return Equals(a512);
            if(other is FixedString4096Bytes a4096) return Equals(a4096);
            return false;
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS"), Conditional("UNITY_DOTS_DEBUG")]
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static void CheckNull(void* dataPtr)
        {
            if (dataPtr == null)
            {
                throw new InvalidOperationException("NativeText has yet to be created or has been destroyed!");
            }
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS")]
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        readonly void CheckRead()
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            AtomicSafetyHandle.CheckReadAndThrow(m_Safety);
#endif
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS")]
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        void CheckWrite()
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            AtomicSafetyHandle.CheckWriteAndThrow(m_Safety);
#endif
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS")]
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        readonly void CheckWriteAndBumpSecondaryVersion()
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            AtomicSafetyHandle.CheckWriteAndBumpSecondaryVersion(m_Safety);
#endif
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS"), Conditional("UNITY_DOTS_DEBUG")]
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        void CheckIndexInRange(int index)
        {
            if (index < 0)
                throw new IndexOutOfRangeException($"Index {index} must be positive.");
            if (index >= Length)
                throw new IndexOutOfRangeException($"Index {index} is out of range in NativeText of {Length} length.");
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS"), Conditional("UNITY_DOTS_DEBUG")]
        void ThrowCopyError(CopyError error, String source)
        {
            throw new ArgumentException($"NativeText: {error} while copying \"{source}\"");
        }

        /// <summary>
        /// A read-only alias for the value of a NativeText. Does not have its own allocated storage.
        /// </summary>
        [NativeContainer]
        [NativeContainerIsReadOnly]
        public unsafe struct ReadOnly
            : INativeList<byte>
            , IUTF8Bytes
            , IComparable<String>
            , IEquatable<String>
            , IComparable<NativeText>
            , IEquatable<NativeText>
            , IComparable<FixedString32Bytes>
            , IEquatable<FixedString32Bytes>
            , IComparable<FixedString64Bytes>
            , IEquatable<FixedString64Bytes>
            , IComparable<FixedString128Bytes>
            , IEquatable<FixedString128Bytes>
            , IComparable<FixedString512Bytes>
            , IEquatable<FixedString512Bytes>
            , IComparable<FixedString4096Bytes>
            , IEquatable<FixedString4096Bytes>
        {
            [NativeDisableUnsafePtrRestriction]
            internal UnsafeText* m_Data;

#if ENABLE_UNITY_COLLECTIONS_CHECKS
            internal AtomicSafetyHandle m_Safety;
            internal static readonly SharedStatic<int> s_staticSafetyId = SharedStatic<int>.GetOrCreate<NativeText.ReadOnly>();

            internal ReadOnly(UnsafeText* text, AtomicSafetyHandle safety)
            {
                m_Data = text;
                m_Safety = safety;
                CollectionHelper.SetStaticSafetyId(ref m_Safety, ref s_staticSafetyId.Data, "Unity.Collections.NativeText.ReadOnly");
            }
#else
            internal ReadOnly(UnsafeText* text)
            {
                m_Data = text;
            }
#endif

            /// <summary>
            /// The current capacity in bytes of this string.
            /// </summary>
            /// <remarks>
            /// The null-terminator byte is not included in the capacity, so the string's character buffer is `Capacity + 1` in size.
            /// </remarks>
            /// <value>The current capacity in bytes of the string.</value>
            /// <exception cref="NotSupportedException">Thrown if ENABLE_UNITY_COLLECTIONS_CHECKS is defined and a write is attempted.</exception>
            public int Capacity
            {
                [MethodImpl(MethodImplOptions.AggressiveInlining)]
                readonly get
                {
                    CheckNull(m_Data);
                    CheckRead();
                    return m_Data->Capacity;
                }

                set
                {
                    ErrorWrite();
                }
            }

            /// <summary>
            /// Whether this string has no characters.
            /// </summary>
            /// <value>True if this string has no characters or if the string has not been constructed.</value>
            public bool IsEmpty
            {
                [MethodImpl(MethodImplOptions.AggressiveInlining)]
                readonly get
                {
                    if (m_Data == null)
                    {
                        return true;
                    }

                    CheckRead();
                    return m_Data->IsEmpty;
                }

                set
                {
                    ErrorWrite();
                }
            }

            /// <summary>
            /// The current length in bytes of this string.
            /// </summary>
            /// <remarks>
            /// The length does not include the null terminator byte.
            /// </remarks>
            /// <value>The current length in bytes of the UTF-8 encoded string.</value>
            /// <exception cref="NotSupportedException">Thrown if ENABLE_UNITY_COLLECTIONS_CHECKS is defined and a write is attempted.</exception>
            public int Length
            {
                [MethodImpl(MethodImplOptions.AggressiveInlining)]
                readonly get
                {
                    CheckNull(m_Data);
                    CheckRead();
                    return m_Data->Length;
                }
                set
                {
                    ErrorWrite();
                }
            }

            /// <summary>
            /// The byte at an index.
            /// </summary>
            /// <param name="index">A zero-based byte index.</param>
            /// <value>The byte at the index.</value>
            /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
            /// <exception cref="NotSupportedException">Thrown if ENABLE_UNITY_COLLECTIONS_CHECKS is defined and a write is attempted.</exception>
            public byte this[int index]
            {
                [MethodImpl(MethodImplOptions.AggressiveInlining)]
                readonly get
                {
                    CheckNull(m_Data);
                    CheckRead();
                    return m_Data->ElementAt(index);
                }

                [MethodImpl(MethodImplOptions.AggressiveInlining)]
                set
                {
                    ErrorWrite();
                }
            }

            /// <summary>
            /// Sets the length to 0. For a NativeText.Readonly this function does nothing, unless safety checks are enabled (in which case it throws).
            /// </summary>
            /// <exception cref="NotSupportedException">Thrown if ENABLE_UNITY_COLLECTIONS_CHECKS is defined.</exception>
            public void Clear()
            {
                ErrorWrite();
            }

            /// <summary>
            /// Returns a reference to the byte (not character) at an index. Unsupported by NativeText.ReadOnly.
            /// </summary>
            /// <remarks>
            /// This function is a no-op when ENABLE_UNITY_COLLECTIONS_CHECKS is not defined, throws otherwise.
            /// </remarks>
            /// <param name="index">A byte index.</param>
            /// <returns>A reference to the byte at the index.</returns>
            /// <exception cref="NotSupportedException">Thrown when called. This operation is not supported.</exception>
            public ref byte ElementAt(int index)
            {
                throw new NotSupportedException("Trying to retrieve non-readonly ref to NativeText.ReadOnly data. This is not permitted.");
            }

            /// <summary>
            /// Returns a pointer to this string's character buffer.
            /// </summary>
            /// <remarks>
            /// The pointer is made invalid by operations that reallocate the character buffer, such as setting <see cref="Capacity"/>.
            /// </remarks>
            /// <returns>A pointer to this string's character buffer.</returns>
            public byte* GetUnsafePtr()
            {
                CheckNull(m_Data);
                CheckRead();
                return m_Data->GetUnsafePtr();
            }

            /// <summary>
            /// Attempt to set the length in bytes of this string. For NativeText.ReadOnly this function is a no-op and always returns false.
            /// </summary>
            /// <param name="newLength">The new length in bytes of the string.</param>
            /// <param name="clearOptions">Whether any bytes added should be zeroed out.</param>
            /// <returns>Always false.</returns>
            /// <exception cref="NotSupportedException">Thrown if ENABLE_UNITY_COLLECTIONS_CHECKS is defined.</exception>
            public bool TryResize(int newLength, NativeArrayOptions clearOptions = NativeArrayOptions.ClearMemory)
            {
                ErrorWrite();
                return false;
            }

            [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS"), Conditional("UNITY_DOTS_DEBUG")]
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            internal static void CheckNull(void* dataPtr)
            {
                if (dataPtr == null)
                {
                    throw new InvalidOperationException("NativeText.ReadOnly has yet to be created or has been destroyed!");
                }
            }

            [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS")]
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            readonly void CheckRead()
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                // Ensure we are allowed to read
                AtomicSafetyHandle.CheckReadAndThrow(m_Safety);
#endif
            }

            [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS"), Conditional("UNITY_DOTS_DEBUG")]
            void ErrorWrite()
            {
                throw new NotSupportedException("Trying to write to a NativeText.ReadOnly. Write operations are not permitted and are ignored.");
            }

            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            [ExcludeFromBurstCompatTesting("Takes managed string")]
            public int CompareTo(String other)
            {
                CheckNull(m_Data);
                CheckRead();
                return m_Data->ToString().CompareTo(other);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            [ExcludeFromBurstCompatTesting("Takes managed string")]
            public bool Equals(String other)
            {
                CheckNull(m_Data);
                CheckRead();
                return m_Data->ToString().Equals(other);
            }

            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(ReadOnly other)
            {
                CheckNull(m_Data);
                CheckRead();
                return FixedStringMethods.CompareTo(ref *m_Data, *other.m_Data);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(ReadOnly other)
            {
                CheckNull(m_Data);
                CheckRead();
                return FixedStringMethods.Equals(ref *m_Data, *other.m_Data);
            }

            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(NativeText other)
            {
                CheckNull(m_Data);
                CheckRead();
                return FixedStringMethods.CompareTo(ref this, *other.m_Data);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(NativeText other)
            {
                CheckNull(m_Data);
                CheckRead();
                return FixedStringMethods.Equals(ref this, *other.m_Data);
            }

            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(FixedString32Bytes other)
            {
                return FixedStringMethods.CompareTo(ref this, other);
            }


            /// <summary>
            /// Returns true if two strings are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are equal.</returns>
            public static bool operator ==(in ReadOnly a, in FixedString32Bytes b)
            {
                CheckNull(a.m_Data);
                a.CheckRead();
                unsafe {
                    var aref = *a.m_Data;
                    int alen = aref.Length;
                    int blen = b.utf8LengthInBytes;
                    byte* aptr = (byte*) aref.GetUnsafePtr();
                    byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                    return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
                }
            }

            /// <summary>
            /// Returns true if two strings are unequal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are unequal.</returns>
            public static bool operator !=(in ReadOnly a, in FixedString32Bytes b)
            {
                return !(a == b);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(FixedString32Bytes other)
            {
                return this == other;
            }
            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(FixedString64Bytes other)
            {
                return FixedStringMethods.CompareTo(ref this, other);
            }


            /// <summary>
            /// Returns true if two strings are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are equal.</returns>
            public static bool operator ==(in ReadOnly a, in FixedString64Bytes b)
            {
                CheckNull(a.m_Data);
                a.CheckRead();
                unsafe {
                    var aref = *a.m_Data;
                    int alen = aref.Length;
                    int blen = b.utf8LengthInBytes;
                    byte* aptr = (byte*) aref.GetUnsafePtr();
                    byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                    return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
                }
            }

            /// <summary>
            /// Returns true if two strings are unequal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are unequal.</returns>
            public static bool operator !=(in ReadOnly a, in FixedString64Bytes b)
            {
                return !(a == b);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(FixedString64Bytes other)
            {
                return this == other;
            }
            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(FixedString128Bytes other)
            {
                return FixedStringMethods.CompareTo(ref this, other);
            }


            /// <summary>
            /// Returns true if two strings are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are equal.</returns>
            public static bool operator ==(in ReadOnly a, in FixedString128Bytes b)
            {
                CheckNull(a.m_Data);
                a.CheckRead();
                unsafe {
                    var aref = *a.m_Data;
                    int alen = aref.Length;
                    int blen = b.utf8LengthInBytes;
                    byte* aptr = (byte*) aref.GetUnsafePtr();
                    byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                    return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
                }
            }

            /// <summary>
            /// Returns true if two strings are unequal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are unequal.</returns>
            public static bool operator !=(in ReadOnly a, in FixedString128Bytes b)
            {
                return !(a == b);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(FixedString128Bytes other)
            {
                return this == other;
            }
            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(FixedString512Bytes other)
            {
                return FixedStringMethods.CompareTo(ref this, other);
            }


            /// <summary>
            /// Returns true if two strings are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are equal.</returns>
            public static bool operator ==(in ReadOnly a, in FixedString512Bytes b)
            {
                CheckNull(a.m_Data);
                a.CheckRead();
                unsafe {
                    var aref = *a.m_Data;
                    int alen = aref.Length;
                    int blen = b.utf8LengthInBytes;
                    byte* aptr = (byte*) aref.GetUnsafePtr();
                    byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                    return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
                }
            }

            /// <summary>
            /// Returns true if two strings are unequal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are unequal.</returns>
            public static bool operator !=(in ReadOnly a, in FixedString512Bytes b)
            {
                return !(a == b);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(FixedString512Bytes other)
            {
                return this == other;
            }
            /// <summary>
            /// Returns the lexicographical sort order of this string relative to another.
            /// </summary>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
            ///
            /// 0 denotes both strings have the same sort position.<br/>
            /// -1 denotes that this string should be sorted to precede the other.<br/>
            /// +1 denotes that this string should be sorted to follow the other.<br/>
            /// </returns>
            public int CompareTo(FixedString4096Bytes other)
            {
                return FixedStringMethods.CompareTo(ref this, other);
            }


            /// <summary>
            /// Returns true if two strings are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are equal.</returns>
            public static bool operator ==(in ReadOnly a, in FixedString4096Bytes b)
            {
                CheckNull(a.m_Data);
                a.CheckRead();
                unsafe {
                    var aref = *a.m_Data;
                    int alen = aref.Length;
                    int blen = b.utf8LengthInBytes;
                    byte* aptr = (byte*) aref.GetUnsafePtr();
                    byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                    return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
                }
            }

            /// <summary>
            /// Returns true if two strings are unequal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="a">A string to compare.</param>
            /// <param name="b">Another string to compare.</param>
            /// <returns>True if the two strings are unequal.</returns>
            public static bool operator !=(in ReadOnly a, in FixedString4096Bytes b)
            {
                return !(a == b);
            }

            /// <summary>
            /// Returns true if this string and another are equal.
            /// </summary>
            /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if the two strings are equal.</returns>
            public bool Equals(FixedString4096Bytes other)
            {
                return this == other;
            }

            /// <summary>
            /// Returns a managed string copy of this string.
            /// </summary>
            /// <returns>A managed string copy of this string.</returns>
            [ExcludeFromBurstCompatTesting("Returns managed string")]
            public override String ToString()
            {
                if (m_Data == null)
                    return "";
                CheckRead();
                return this.ConvertToString();
            }

            /// <summary>
            /// Returns a hash code of this string.
            /// </summary>
            /// <remarks>The hash code is an integer that is always the same for two equal strings but (very likely) different for two unequal strings.</remarks>
            /// <returns>A hash code of this string.</returns>
            public override int GetHashCode()
            {
                CheckRead();
                return this.ComputeHashCode();
            }

            /// <summary>
            /// Returns true if this string and another object are equal.
            /// </summary>
            /// <remarks>For the object to be equal, it must itself be a managed string, NativeText, or FixedString*N*Bytes.
            ///
            /// Two strings are equal if they have equal length and all their characters match.</remarks>
            /// <param name="other">Another string to compare with.</param>
            /// <returns>True if this string and the object are equal.</returns>
            [ExcludeFromBurstCompatTesting("Takes managed object")]
            public override bool Equals(object other)
            {
                if(ReferenceEquals(null, other)) return false;
                if(other is String aString) return Equals(aString);
                if(other is NativeText aNativeText) return Equals(aNativeText);
                if(other is ReadOnly aReadOnly) return Equals(aReadOnly);
                if(other is FixedString32Bytes a32) return Equals(a32);
                if(other is FixedString64Bytes a64) return Equals(a64);
                if(other is FixedString128Bytes a128) return Equals(a128);
                if(other is FixedString512Bytes a512) return Equals(a512);
                if(other is FixedString4096Bytes a4096) return Equals(a4096);
                return false;
            }

            /// <summary>
            /// A copy of this string as a managed string.
            /// </summary>
            /// <remarks>
            /// For internal use only. Use <see cref="ToString"/> instead.
            /// </remarks>
            /// <value>A copy of this string as a managed string.</value>
            [CreateProperty]
            [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
            [ExcludeFromBurstCompatTesting("Returns managed string")]
            public string Value => ToString();

            /// <summary>
            /// Returns an enumerator for iterating over the characters of the NativeText.
            /// </summary>
            /// <returns>An enumerator for iterating over the characters of the NativeText.</returns>
            public Enumerator GetEnumerator()
            {
                return new Enumerator(this);
            }
        }

        /// <summary>
        /// Returns a readonly version of this NativeText instance.
        /// </summary>
        /// <remarks>ReadOnly containers point to the same underlying data as the NativeText it is made from. Note while ReadOnly contains methods that would write to the string data these methods will perform no writes and/or throw a NotSupportedException.</remarks>
        /// <returns>ReadOnly instance for this.</returns>
        public ReadOnly AsReadOnly()
        {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
            var ash = m_Safety;
            return new ReadOnly(m_Data, ash);
#else
            return new ReadOnly(m_Data);
#endif
        }
    }

    [NativeContainer]
    [GenerateTestsForBurstCompatibility]
    internal unsafe struct NativeTextDispose
    {
        [NativeDisableUnsafePtrRestriction]
        public UnsafeText* m_TextData;

#if ENABLE_UNITY_COLLECTIONS_CHECKS
        public AtomicSafetyHandle m_Safety;
#endif

        public void Dispose()
        {
            UnsafeText.Free(m_TextData);
        }
    }

    [BurstCompile]
    internal unsafe struct NativeTextDisposeJob : IJob
    {
        public NativeTextDispose Data;

        public void Execute()
        {
            Data.Dispose();
        }
    }
}
