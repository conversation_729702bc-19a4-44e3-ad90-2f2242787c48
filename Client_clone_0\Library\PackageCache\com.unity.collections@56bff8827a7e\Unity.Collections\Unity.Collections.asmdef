{"name": "Unity.Collections", "references": ["Unity.Burst", "Unity.Mathematics", "Unity.Properties", "Unity.Collections.LowLevel.ILSupport"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.package-validation-suite", "expression": "0.4.0-preview.6", "define": "UNITY_SKIP_UPDATES_WITH_VALIDATION_SUITE"}, {"name": "Unity", "expression": "2022.2.14f1", "define": "UNITY_2022_2_14F1_OR_NEWER"}, {"name": "Unity", "expression": "2022.2.16f1", "define": "UNITY_2022_2_16F1_OR_NEWER"}], "noEngineReferences": false}