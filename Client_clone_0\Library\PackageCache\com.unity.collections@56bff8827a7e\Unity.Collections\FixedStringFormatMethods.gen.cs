﻿
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections/FixedStringFormatMethods.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using Unity.Collections.LowLevel.Unsafe;

namespace Unity.Collections
{
    /// <summary>
    /// Provides extension methods for FixedString*N*Bytes.
    /// </summary>
    public unsafe static partial class FixedStringMethods
    {
        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0>(ref this T dest, in U format, in T0 arg0)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1>(ref this T dest, in U format, in T0 arg0, in T1 arg1)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T4">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg4">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/), typeof(FixedString128Bytes /*T4*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3, T4>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3, in T4 arg4)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T4 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            case 4: err = dest.Append(in arg4); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T4">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T5">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg4">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg5">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/), typeof(FixedString128Bytes /*T4*/), typeof(FixedString128Bytes /*T5*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3, T4, T5>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3, in T4 arg4, in T5 arg5)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T4 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T5 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            case 4: err = dest.Append(in arg4); break;
                            case 5: err = dest.Append(in arg5); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T4">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T5">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T6">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg4">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg5">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg6">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/), typeof(FixedString128Bytes /*T4*/), typeof(FixedString128Bytes /*T5*/), typeof(FixedString128Bytes /*T6*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3, T4, T5, T6>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3, in T4 arg4, in T5 arg5, in T6 arg6)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T4 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T5 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T6 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            case 4: err = dest.Append(in arg4); break;
                            case 5: err = dest.Append(in arg5); break;
                            case 6: err = dest.Append(in arg6); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T4">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T5">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T6">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T7">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg4">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg5">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg6">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg7">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/), typeof(FixedString128Bytes /*T4*/), typeof(FixedString128Bytes /*T5*/), typeof(FixedString128Bytes /*T6*/), typeof(FixedString128Bytes /*T7*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3, T4, T5, T6, T7>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3, in T4 arg4, in T5 arg5, in T6 arg6, in T7 arg7)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T4 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T5 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T6 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T7 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            case 4: err = dest.Append(in arg4); break;
                            case 5: err = dest.Append(in arg5); break;
                            case 6: err = dest.Append(in arg6); break;
                            case 7: err = dest.Append(in arg7); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T4">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T5">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T6">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T7">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T8">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg4">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg5">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg6">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg7">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg8">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/), typeof(FixedString128Bytes /*T4*/), typeof(FixedString128Bytes /*T5*/), typeof(FixedString128Bytes /*T6*/), typeof(FixedString128Bytes /*T7*/), typeof(FixedString128Bytes /*T8*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3, T4, T5, T6, T7, T8>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3, in T4 arg4, in T5 arg5, in T6 arg6, in T7 arg7, in T8 arg8)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T4 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T5 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T6 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T7 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T8 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            case 4: err = dest.Append(in arg4); break;
                            case 5: err = dest.Append(in arg5); break;
                            case 6: err = dest.Append(in arg6); break;
                            case 7: err = dest.Append(in arg7); break;
                            case 8: err = dest.Append(in arg8); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }

        /// <summary>
        /// Interpolates strings into a format string and appends the result to this string.
        /// </summary>
        /// <remarks>
        /// Similar to `StringBuilder.AppendFormat` but with significant limitations:
        /// - Only supports FixedString*N*Bytes arguments. To use other string types, convert them to FixedString*N*Bytes first.
        /// - Only supports numeric format placeholders of the form `{0}` .. `{N}`.
        /// - No format modifiers (*e.g.* `{0:x}`) are supported.
        ///
        /// The overloads of this method take up to ten strings to interpolate into the format string.
        /// </remarks>
        /// <typeparam name="T">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="U">A FixedString*N*Bytes type.</typeparam>
        /// <typeparam name="T0">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T1">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T2">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T3">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T4">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T5">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T6">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T7">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T8">The type of value to interpolate into the format string.</typeparam>
        /// <typeparam name="T9">The type of value to interpolate into the format string.</typeparam>
        /// <param name="dest">The string to append to.</param>d
        /// <param name="format">A string to be interpolated and appended.</param>
        /// <param name="arg0">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg1">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg2">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg3">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg4">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg5">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg6">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg7">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg8">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <param name="arg9">A FixedString*N*Bytes to interpolate into the format string.</param>
        /// <returns><see cref="FormatError.None"/> if successful.  Otherwise returns the appropriate <see cref="FormatError"/>.</returns>
        [GenerateTestsForBurstCompatibility(GenericTypeArguments = new[] { typeof(FixedString128Bytes /*T*/), typeof(FixedString128Bytes /*U*/), typeof(FixedString128Bytes /*T0*/), typeof(FixedString128Bytes /*T1*/), typeof(FixedString128Bytes /*T2*/), typeof(FixedString128Bytes /*T3*/), typeof(FixedString128Bytes /*T4*/), typeof(FixedString128Bytes /*T5*/), typeof(FixedString128Bytes /*T6*/), typeof(FixedString128Bytes /*T7*/), typeof(FixedString128Bytes /*T8*/), typeof(FixedString128Bytes /*T9*/) })]
        public static unsafe FormatError AppendFormat<T, U, T0, T1, T2, T3, T4, T5, T6, T7, T8, T9>(ref this T dest, in U format, in T0 arg0, in T1 arg1, in T2 arg2, in T3 arg3, in T4 arg4, in T5 arg5, in T6 arg6, in T7 arg7, in T8 arg8, in T9 arg9)
            where T : unmanaged, INativeList<byte>, IUTF8Bytes
            where U : unmanaged, INativeList<byte>, IUTF8Bytes
            where T0 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T1 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T2 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T3 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T4 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T5 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T6 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T7 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T8 : unmanaged, INativeList<byte>, IUTF8Bytes
            where T9 : unmanaged, INativeList<byte>, IUTF8Bytes
        {
            ref var formatRef = ref UnsafeUtilityExtensions.AsRef(in format);
            int formatLength = formatRef.Length;
            byte* formatBytes = formatRef.GetUnsafePtr();
            int i = 0;
            FormatError err = FormatError.None;
            while (i < formatLength)
            {
                byte currByte = formatBytes[i++];
                if (currByte == (byte)'{')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        return FormatError.BadFormatSpecifier;

                    if (currByte >= (byte)'0' && currByte <= (byte)'9' && i < formatLength && formatBytes[i++] == (byte)'}')
                    {
                        switch (currByte - (byte)'0')
                        {
                            case 0: err = dest.Append(in arg0); break;
                            case 1: err = dest.Append(in arg1); break;
                            case 2: err = dest.Append(in arg2); break;
                            case 3: err = dest.Append(in arg3); break;
                            case 4: err = dest.Append(in arg4); break;
                            case 5: err = dest.Append(in arg5); break;
                            case 6: err = dest.Append(in arg6); break;
                            case 7: err = dest.Append(in arg7); break;
                            case 8: err = dest.Append(in arg8); break;
                            case 9: err = dest.Append(in arg9); break;
                            default: err = FormatError.BadFormatSpecifier; break;
                        }
                    }
                    else if (currByte == (byte)'{')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else if (currByte == (byte)'}')
                {
                    if (i < formatLength)
                        currByte = formatBytes[i++];
                    else
                        err = FormatError.BadFormatSpecifier;

                    if (currByte == (byte)'}')
                        err = dest.AppendRawByte(currByte);
                    else
                        err = FormatError.BadFormatSpecifier;
                }
                else
                    err = dest.AppendRawByte(currByte);

                if (err != FormatError.None)
                    return err;
            }

            return FormatError.None;
        }


    }
}
