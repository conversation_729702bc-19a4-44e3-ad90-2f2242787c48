{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 32048, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 32048, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 32048, "tid": 206, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 32048, "tid": 206, "ts": 1755111596971938, "dur": 1296, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 32048, "tid": 206, "ts": 1755111596977116, "dur": 855, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 32048, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 32048, "tid": 1, "ts": 1755111596101801, "dur": 5714, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 32048, "tid": 1, "ts": 1755111596107519, "dur": 30754, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 32048, "tid": 1, "ts": 1755111596138282, "dur": 28837, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 32048, "tid": 206, "ts": 1755111596977978, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 32048, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596099672, "dur": 11264, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596110939, "dur": 851211, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596112014, "dur": 2468, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596114488, "dur": 1352, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596115844, "dur": 11815, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596127668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596127670, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596127810, "dur": 540, "ph": "X", "name": "ProcessMessages 1607", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128354, "dur": 155, "ph": "X", "name": "ReadAsync 1607", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128513, "dur": 8, "ph": "X", "name": "ProcessMessages 9619", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128522, "dur": 49, "ph": "X", "name": "ReadAsync 9619", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128575, "dur": 2, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128578, "dur": 43, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128624, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128626, "dur": 44, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128673, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128675, "dur": 41, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128719, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128722, "dur": 43, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128767, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128771, "dur": 41, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128816, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128818, "dur": 39, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128859, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128862, "dur": 60, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128926, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128929, "dur": 41, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128973, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596128977, "dur": 44, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129024, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129026, "dur": 39, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129067, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129069, "dur": 38, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129110, "dur": 36, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129148, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129150, "dur": 39, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129191, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129194, "dur": 36, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129232, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129233, "dur": 38, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129274, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129276, "dur": 32, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129310, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129312, "dur": 36, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129350, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129352, "dur": 37, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129391, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129394, "dur": 37, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129433, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129435, "dur": 37, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129474, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129476, "dur": 38, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129516, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129518, "dur": 35, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129555, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129557, "dur": 38, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129598, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129600, "dur": 37, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129639, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129641, "dur": 39, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129681, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129683, "dur": 36, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129721, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129723, "dur": 65, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129792, "dur": 1, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129795, "dur": 44, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129842, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596129844, "dur": 410, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130256, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130259, "dur": 143, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130405, "dur": 6, "ph": "X", "name": "ProcessMessages 7160", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130412, "dur": 66, "ph": "X", "name": "ReadAsync 7160", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130481, "dur": 1, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130484, "dur": 72, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130560, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130562, "dur": 79, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130644, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130646, "dur": 77, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130726, "dur": 1, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130728, "dur": 76, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130806, "dur": 1, "ph": "X", "name": "ProcessMessages 1115", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130809, "dur": 74, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130885, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130888, "dur": 68, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130958, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596130959, "dur": 69, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131031, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131033, "dur": 68, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131104, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131106, "dur": 76, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131184, "dur": 1, "ph": "X", "name": "ProcessMessages 1142", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131186, "dur": 78, "ph": "X", "name": "ReadAsync 1142", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131267, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131269, "dur": 42, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131313, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131315, "dur": 296, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131614, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131660, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131662, "dur": 38, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131702, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131704, "dur": 45, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131751, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131753, "dur": 43, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131799, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131800, "dur": 35, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131839, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131885, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131886, "dur": 74, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131964, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596131966, "dur": 40, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132009, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132012, "dur": 39, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132053, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132055, "dur": 32, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132091, "dur": 38, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132132, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132135, "dur": 38, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132175, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132177, "dur": 33, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132213, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132215, "dur": 37, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132256, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132259, "dur": 37, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132298, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132300, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132334, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132374, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132376, "dur": 36, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132415, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132417, "dur": 39, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132458, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132460, "dur": 36, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132499, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132501, "dur": 31, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132534, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132536, "dur": 35, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132574, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132576, "dur": 37, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132615, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132617, "dur": 34, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132654, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132656, "dur": 37, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132696, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132698, "dur": 34, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132734, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132736, "dur": 119, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132859, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132899, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132901, "dur": 37, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132941, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132943, "dur": 36, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132982, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596132984, "dur": 37, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133023, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133026, "dur": 32, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133060, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133062, "dur": 36, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133100, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133102, "dur": 37, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133142, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133144, "dur": 33, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133180, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133182, "dur": 37, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133221, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133223, "dur": 33, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133258, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133260, "dur": 37, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133302, "dur": 35, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133340, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133342, "dur": 33, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133378, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133380, "dur": 37, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133419, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133422, "dur": 36, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133460, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133462, "dur": 67, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133533, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133535, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133588, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133591, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133642, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133644, "dur": 41, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133689, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133691, "dur": 41, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133734, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133736, "dur": 36, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133775, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133777, "dur": 42, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133822, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133825, "dur": 38, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133866, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133868, "dur": 36, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133906, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133909, "dur": 37, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133948, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133949, "dur": 32, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133984, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596133985, "dur": 179, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134168, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134206, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134208, "dur": 62, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134272, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134274, "dur": 35, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134312, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134314, "dur": 34, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134350, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134352, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134386, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134388, "dur": 201, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134593, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134632, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134634, "dur": 36, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134673, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134675, "dur": 35, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134712, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134714, "dur": 34, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134750, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134752, "dur": 33, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134787, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134789, "dur": 38, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134830, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134833, "dur": 39, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134874, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134876, "dur": 36, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134914, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134916, "dur": 40, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134958, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134960, "dur": 33, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134995, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596134998, "dur": 32, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135032, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135034, "dur": 34, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135071, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135073, "dur": 38, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135114, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135116, "dur": 36, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135155, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135157, "dur": 36, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135196, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135198, "dur": 30, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135233, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135272, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135274, "dur": 38, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135315, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135317, "dur": 36, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135356, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135358, "dur": 36, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135396, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135399, "dur": 34, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135435, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135437, "dur": 34, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135473, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135475, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135521, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135563, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135565, "dur": 35, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135603, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135605, "dur": 37, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135644, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135646, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135684, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135686, "dur": 47, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135735, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135737, "dur": 41, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135780, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135782, "dur": 37, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135822, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135824, "dur": 36, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135862, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135864, "dur": 34, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135900, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135902, "dur": 32, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135941, "dur": 50, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135994, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596135996, "dur": 41, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136040, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136042, "dur": 38, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136083, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136085, "dur": 33, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136120, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136124, "dur": 29, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136156, "dur": 35, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136193, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136195, "dur": 38, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136236, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136238, "dur": 34, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136274, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136276, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136315, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136316, "dur": 29, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136348, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136350, "dur": 30, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136384, "dur": 35, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136421, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136423, "dur": 51, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136477, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136479, "dur": 33, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136514, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136516, "dur": 35, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136553, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136555, "dur": 29, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136586, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136588, "dur": 296, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136888, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136948, "dur": 2, "ph": "X", "name": "ProcessMessages 2121", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596136951, "dur": 176, "ph": "X", "name": "ReadAsync 2121", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137131, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137171, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137172, "dur": 40, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137214, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137216, "dur": 35, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137254, "dur": 34, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137291, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137292, "dur": 34, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137328, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137330, "dur": 33, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137366, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137368, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137405, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137407, "dur": 33, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137442, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137444, "dur": 41, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137488, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137490, "dur": 43, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137536, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137538, "dur": 37, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137577, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137579, "dur": 39, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137625, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137674, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137676, "dur": 45, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137724, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137727, "dur": 37, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137766, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137768, "dur": 34, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137805, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137807, "dur": 29, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137839, "dur": 36, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137878, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137881, "dur": 33, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137916, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137918, "dur": 36, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137956, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137958, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137992, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596137994, "dur": 32, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138029, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138030, "dur": 208, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138242, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138282, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138284, "dur": 63, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138349, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138351, "dur": 41, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138395, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138397, "dur": 39, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138438, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138440, "dur": 32, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138474, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138476, "dur": 37, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138514, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138516, "dur": 36, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138554, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138556, "dur": 37, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138595, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138597, "dur": 39, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138639, "dur": 35, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138677, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138679, "dur": 33, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138714, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138716, "dur": 32, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138751, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138790, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138792, "dur": 33, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138827, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138829, "dur": 32, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138863, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138865, "dur": 28, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138895, "dur": 1, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138897, "dur": 31, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138930, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138932, "dur": 35, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596138971, "dur": 36, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139008, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139010, "dur": 34, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139047, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139048, "dur": 36, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139086, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139087, "dur": 28, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139119, "dur": 34, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139157, "dur": 30, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139189, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139191, "dur": 33, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139225, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139227, "dur": 32, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139273, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139275, "dur": 36, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139313, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139315, "dur": 29, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139348, "dur": 200, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139551, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139592, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139593, "dur": 34, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139630, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139632, "dur": 34, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139668, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139670, "dur": 35, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139709, "dur": 31, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139742, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139744, "dur": 33, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139781, "dur": 31, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139816, "dur": 31, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139850, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139851, "dur": 36, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139890, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139891, "dur": 36, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139929, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139931, "dur": 34, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139968, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596139969, "dur": 30, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140001, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140003, "dur": 33, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140038, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140040, "dur": 35, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140078, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140080, "dur": 35, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140117, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140120, "dur": 36, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140158, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140160, "dur": 33, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140195, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140198, "dur": 31, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140231, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140233, "dur": 36, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140271, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140273, "dur": 31, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140306, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140308, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140345, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140347, "dur": 35, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140384, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140386, "dur": 33, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140421, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140422, "dur": 29, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140454, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140455, "dur": 192, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140651, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140691, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140693, "dur": 33, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140728, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140730, "dur": 32, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140764, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140766, "dur": 35, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140803, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140805, "dur": 32, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140839, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596140840, "dur": 200, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141043, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141080, "dur": 30, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141113, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141114, "dur": 37, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141153, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141155, "dur": 32, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141190, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141192, "dur": 35, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141229, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141231, "dur": 28, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141261, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141262, "dur": 202, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141468, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141506, "dur": 34, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141543, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141545, "dur": 35, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141581, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141583, "dur": 36, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141621, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141623, "dur": 30, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141655, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141657, "dur": 118, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141778, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141817, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141818, "dur": 34, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141855, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141857, "dur": 29, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141889, "dur": 39, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141930, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141932, "dur": 34, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141968, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596141969, "dur": 35, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142007, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142008, "dur": 34, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142045, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142046, "dur": 34, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142083, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142085, "dur": 35, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142122, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142123, "dur": 35, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142161, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142164, "dur": 52, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142220, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142222, "dur": 40, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142265, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142267, "dur": 39, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142308, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142310, "dur": 41, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142353, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142355, "dur": 37, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142395, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142396, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142432, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142434, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142478, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142481, "dur": 37, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142520, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596142522, "dur": 476, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143002, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143048, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143050, "dur": 89, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143143, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143187, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143190, "dur": 34, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143227, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143228, "dur": 496, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143728, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143771, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143773, "dur": 43, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143818, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143820, "dur": 40, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143863, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143865, "dur": 37, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143905, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143906, "dur": 42, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143950, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143952, "dur": 33, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143988, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596143990, "dur": 37, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144030, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144031, "dur": 444, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144480, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144530, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144532, "dur": 41, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144575, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144577, "dur": 32, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144612, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596144614, "dur": 488, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145106, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145147, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145149, "dur": 38, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145189, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145192, "dur": 35, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145229, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145232, "dur": 645, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145881, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145920, "dur": 37, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145959, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596145961, "dur": 39, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146003, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146005, "dur": 31, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146039, "dur": 427, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146471, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146511, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146513, "dur": 34, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146562, "dur": 35, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146600, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596146603, "dur": 429, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147037, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147079, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147081, "dur": 38, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147122, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147124, "dur": 502, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147630, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147672, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147674, "dur": 39, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147715, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147717, "dur": 35, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596147756, "dur": 432, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148192, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148233, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148235, "dur": 40, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148277, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148279, "dur": 501, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148784, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148821, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148823, "dur": 36, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148862, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148864, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148905, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148907, "dur": 31, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148941, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596148943, "dur": 500, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149448, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149497, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149499, "dur": 37, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149539, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149540, "dur": 31, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149573, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596149575, "dur": 469, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150047, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150084, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150086, "dur": 33, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150123, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150125, "dur": 31, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150158, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150159, "dur": 479, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150641, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150680, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150681, "dur": 32, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150716, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150717, "dur": 30, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150750, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596150752, "dur": 278, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151033, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151070, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151071, "dur": 28, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151102, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151104, "dur": 36, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151143, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151181, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151182, "dur": 28, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151214, "dur": 453, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151671, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151726, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151729, "dur": 32, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151763, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151764, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151802, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151804, "dur": 34, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151840, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151841, "dur": 33, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151878, "dur": 32, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151912, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151914, "dur": 32, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151950, "dur": 30, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596151984, "dur": 510, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152498, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152536, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152538, "dur": 32, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152573, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152574, "dur": 31, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152608, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596152609, "dur": 520, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153133, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153170, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153172, "dur": 33, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153209, "dur": 31, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153242, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153244, "dur": 400, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153647, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153684, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153686, "dur": 31, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153720, "dur": 33, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153755, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596153757, "dur": 462, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154222, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154258, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154260, "dur": 33, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154295, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154297, "dur": 31, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154330, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154332, "dur": 459, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154794, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154833, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154834, "dur": 35, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154871, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154873, "dur": 30, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154905, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154906, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154937, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154975, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596154977, "dur": 29, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155009, "dur": 481, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155494, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155538, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155541, "dur": 163, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155707, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155752, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155754, "dur": 34, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155792, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596155793, "dur": 613, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156412, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156459, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156461, "dur": 42, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156506, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156509, "dur": 47, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156559, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156560, "dur": 261, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156825, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156872, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156874, "dur": 40, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156917, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156918, "dur": 31, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156952, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596156954, "dur": 495, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157454, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157497, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157499, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157537, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157538, "dur": 31, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157574, "dur": 53, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157629, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157631, "dur": 41, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157675, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157678, "dur": 39, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157719, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157721, "dur": 31, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157756, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157757, "dur": 33, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157793, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596157795, "dur": 461, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158261, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158308, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158310, "dur": 44, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158359, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158362, "dur": 42, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158407, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158409, "dur": 478, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158891, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158970, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596158973, "dur": 119, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159095, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159134, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159136, "dur": 45, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159185, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159224, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159226, "dur": 485, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159716, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159757, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159759, "dur": 159, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159922, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159963, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159965, "dur": 28, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159995, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596159996, "dur": 485, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160486, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160529, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160531, "dur": 60, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160593, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160595, "dur": 33, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160630, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596160632, "dur": 453, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161088, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161129, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161130, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161168, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161170, "dur": 37, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161210, "dur": 32, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161247, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161249, "dur": 430, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161683, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161720, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161722, "dur": 33, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161758, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161759, "dur": 33, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161794, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161796, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596161825, "dur": 491, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162320, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162362, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162364, "dur": 34, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162400, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162402, "dur": 32, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162436, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162438, "dur": 32, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162472, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162473, "dur": 29, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162505, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162507, "dur": 465, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596162975, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163015, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163016, "dur": 39, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163058, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163059, "dur": 151, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163215, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163259, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163261, "dur": 30, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163295, "dur": 599, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163898, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163937, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163939, "dur": 35, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163977, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596163978, "dur": 141, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164123, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164163, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164165, "dur": 38, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164207, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164211, "dur": 607, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164823, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164913, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164915, "dur": 42, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596164961, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165004, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165006, "dur": 32, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165042, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165043, "dur": 477, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165526, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165565, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165567, "dur": 39, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165608, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596165610, "dur": 433, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166047, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166085, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166086, "dur": 34, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166123, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166125, "dur": 37, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166164, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166165, "dur": 34, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166202, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166204, "dur": 463, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166671, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166712, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166713, "dur": 34, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166750, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166751, "dur": 147, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166902, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166949, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166951, "dur": 42, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166995, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596166997, "dur": 37, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167037, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167039, "dur": 37, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167079, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167080, "dur": 31, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167114, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167116, "dur": 35, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167155, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167156, "dur": 500, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167660, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167742, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596167745, "dur": 257, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168008, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168063, "dur": 245, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168311, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168367, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168370, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168415, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168418, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168467, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168469, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168515, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168518, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168560, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168562, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168606, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168652, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168654, "dur": 53, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168711, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168715, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168760, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168763, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168812, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168815, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168860, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168863, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168908, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168912, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168949, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168951, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168992, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596168994, "dur": 35, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169032, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169034, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169074, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169112, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169114, "dur": 55, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169173, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169176, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169217, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169220, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169259, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169262, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169298, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169300, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169339, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169342, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169380, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169382, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169432, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169434, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169475, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169547, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169592, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169595, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169667, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169670, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169728, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169733, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169777, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169779, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169820, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169822, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169883, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169887, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169931, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169934, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169970, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596169972, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170013, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170016, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170061, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170064, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170122, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170126, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170169, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170171, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170225, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170228, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170279, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170281, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170317, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170319, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170361, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170363, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170400, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170401, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170442, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170445, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170501, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170543, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170545, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170589, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170592, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170650, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170652, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170740, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170785, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170788, "dur": 82, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170875, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170924, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170926, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170968, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596170971, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171015, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171057, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171059, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171150, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171152, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171212, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171215, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171261, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171263, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171325, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171327, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171392, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171394, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171437, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171441, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171484, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171486, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171527, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171529, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171627, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171722, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171725, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171770, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171773, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171813, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171815, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171856, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171858, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171908, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171911, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171957, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596171959, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172003, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172046, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172048, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172093, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172095, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172142, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172145, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172198, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172201, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172247, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172250, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172300, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172303, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172370, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172373, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172416, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172419, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172469, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172472, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172527, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172531, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172584, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172587, "dur": 62, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172653, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172656, "dur": 60, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172719, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172722, "dur": 41, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172767, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172770, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172826, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172829, "dur": 46, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172878, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172881, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172926, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172929, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172977, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596172979, "dur": 42, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173025, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173027, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173073, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173076, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173122, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173124, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173173, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173177, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173222, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173224, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173269, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173271, "dur": 40, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173314, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173316, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173358, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173361, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173402, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173404, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173460, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173463, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173508, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173510, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173555, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173557, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173599, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173601, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173647, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173649, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173697, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173700, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173749, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173752, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173810, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173812, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173855, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173858, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173903, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173905, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173946, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173949, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596173996, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596174038, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596174040, "dur": 368, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596174414, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596174453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596174455, "dur": 6449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596180911, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596180915, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596180973, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596180976, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596181027, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596181028, "dur": 1239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182272, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182275, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182325, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182327, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182376, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182378, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182426, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182601, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596182677, "dur": 704, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183387, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183430, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183432, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183515, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183517, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183594, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183688, "dur": 296, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596183989, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184028, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184031, "dur": 157, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184194, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184233, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184235, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184274, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184276, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184350, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184353, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184394, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184396, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184437, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184439, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184489, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184525, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184528, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184561, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596184563, "dur": 586, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185155, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185200, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185202, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185248, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185251, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185295, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185297, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185336, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185338, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185375, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185377, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185416, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185418, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185493, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185532, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185534, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185574, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185609, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185645, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185647, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185710, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185751, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185790, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185792, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185827, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185829, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596185987, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186024, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186026, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186063, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186065, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186104, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186139, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186235, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186272, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186274, "dur": 202, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186481, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186520, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186556, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186558, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186593, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186595, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186763, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186795, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186797, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186873, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186922, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186925, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596186986, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187026, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187028, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187066, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187068, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187213, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187252, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187255, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187306, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187340, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187387, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187436, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187471, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187473, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187588, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187626, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187628, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187674, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187676, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187716, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187719, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187757, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187760, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187797, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187835, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187837, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187876, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187879, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187916, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187918, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187953, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187955, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187994, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596187997, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188033, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188034, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188069, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188071, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188107, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188109, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188151, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188195, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188198, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188246, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188248, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188289, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188291, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188373, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188418, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188420, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188547, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188590, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188592, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188664, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188709, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188711, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188766, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188768, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188817, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188819, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188865, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188867, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596188990, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189030, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189031, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189106, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189261, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189303, "dur": 525, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189831, "dur": 69, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189903, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596189905, "dur": 342, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596190250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596190253, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596190289, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596190291, "dur": 142, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596190438, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596190498, "dur": 624, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596191127, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596191171, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596191174, "dur": 124192, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596315374, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596315378, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596315432, "dur": 1439, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596316874, "dur": 10410, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327293, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327297, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327351, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327453, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327517, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327520, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327558, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327560, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327668, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327709, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327711, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327746, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596327748, "dur": 475, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596328229, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596328267, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596328269, "dur": 1779, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330056, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330099, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330102, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330244, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330282, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330284, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330322, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330325, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330366, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330369, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330560, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330600, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596330603, "dur": 430, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331039, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331074, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331076, "dur": 854, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331934, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331936, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331978, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596331980, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332272, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332311, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332527, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332566, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332863, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332926, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332929, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332974, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596332976, "dur": 454, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333435, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333437, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333481, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333483, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333524, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333526, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333559, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333561, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333597, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333599, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333669, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333711, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333750, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333752, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333793, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333832, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333835, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333878, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333881, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333933, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333936, "dur": 59, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596333999, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334002, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334048, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334051, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334094, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334135, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334138, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334180, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334182, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334226, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334229, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334273, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334276, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334316, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334319, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334355, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334357, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334424, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334469, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334471, "dur": 267, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334743, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334780, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334902, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334949, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334953, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596334995, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335206, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335263, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335265, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335299, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335301, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335390, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335434, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335436, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335507, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335509, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335547, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335549, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596335587, "dur": 57507, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596393105, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596393110, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596393168, "dur": 21, "ph": "X", "name": "ProcessMessages 1076", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596393191, "dur": 19111, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596412311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596412315, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596412367, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596412370, "dur": 506476, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596918854, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596918857, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596918943, "dur": 19, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596918964, "dur": 27311, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596946282, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596946286, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596946398, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596946402, "dur": 1493, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596947900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596947902, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596948003, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596948029, "dur": 3409, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596951446, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596951450, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596951545, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596951550, "dur": 527, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952085, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952139, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952168, "dur": 507, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952680, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952682, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596952733, "dur": 414, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 32048, "tid": 12884901888, "ts": 1755111596953152, "dur": 8233, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 32048, "tid": 206, "ts": 1755111596978003, "dur": 2420, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 32048, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 32048, "tid": 8589934592, "ts": 1755111596096579, "dur": 70953, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 32048, "tid": 8589934592, "ts": 1755111596167534, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 32048, "tid": 8589934592, "ts": 1755111596167538, "dur": 1781, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 32048, "tid": 206, "ts": 1755111596980446, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 32048, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 32048, "tid": 4294967296, "ts": 1755111596077317, "dur": 885792, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 32048, "tid": 4294967296, "ts": 1755111596081277, "dur": 8899, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 32048, "tid": 4294967296, "ts": 1755111596963379, "dur": 4928, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 32048, "tid": 4294967296, "ts": 1755111596966192, "dur": 179, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 32048, "tid": 4294967296, "ts": 1755111596968400, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 32048, "tid": 206, "ts": 1755111596980455, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755111596108849, "dur": 2296, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596111158, "dur": 16006, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596127377, "dur": 74, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755111596127451, "dur": 695, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596128989, "dur": 134, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111596129673, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111596130901, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FC3813CDB6AE67C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111596131559, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755111596132525, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755111596133944, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755111596134064, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755111596143648, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596152304, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/ScriptablePacker.dll"}}, {"pid": 12345, "tid": 0, "ts": 1755111596156138, "dur": 198, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596159531, "dur": 188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596160364, "dur": 187, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596163643, "dur": 199, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596164566, "dur": 187, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596165446, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1755111596167338, "dur": 191, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755111596128184, "dur": 40104, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596168301, "dur": 784458, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596952760, "dur": 130, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596952967, "dur": 52, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596953150, "dur": 85, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596953278, "dur": 1207, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755111596128219, "dur": 40109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596168349, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1755111596168611, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DF6F7FF917E347B3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169072, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596169332, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9AB55CF056145574.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169507, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_3787BA530AC70137.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169588, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_A7499BA3B50749DD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169643, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596169700, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8E87AC3CC97FF6E8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169766, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8E87AC3CC97FF6E8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169830, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_91BF7E65A37C8C47.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169943, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596169998, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596170148, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596170417, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6126AAA2007E3BC1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596170514, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2B2A94CF86E88A71.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596170569, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596170648, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596170759, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F327315904C52B00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596170875, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755111596171343, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596171488, "dur": 9424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596180914, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596181506, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596181620, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596182239, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596182792, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596182930, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596184021, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596184826, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596185007, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596185697, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596185762, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596186177, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596186383, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596186630, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596187592, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596188057, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596188295, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596188392, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596188495, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1755111596188616, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1755111596188776, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596190048, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755111596190235, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596190879, "dur": 134400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596325293, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596328091, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596330754, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596330931, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596333423, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755111596333485, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755111596335870, "dur": 616863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596128395, "dur": 39957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596168362, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755111596168639, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_AF66D8329BCEF71E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169118, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596169244, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E0C67EE87749D19A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169381, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_334958EB079AC540.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169475, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1FFECC68464BBF36.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169537, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596169594, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169687, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169794, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596169858, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B3100D568D19DE53.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596169914, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596170007, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_8F866AA12C4C5E35.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596170241, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_8F866AA12C4C5E35.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596170303, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_FFA66C83E76ECCA7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596170382, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596170453, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B25B7B96E5346C1F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596170741, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111596171006, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596171109, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755111596171541, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111596172273, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111596172775, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755111596173071, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1755111596173437, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111596173779, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3258459190130463912.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111596174122, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755111596174487, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596175445, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596176834, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596177100, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596177577, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\ClipModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1755111596177414, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596178332, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596178649, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596179184, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596179622, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596180980, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91e7def251e0\\Editor\\UI\\TilePaletteActiveTargetsButton.cs"}}, {"pid": 12345, "tid": 2, "ts": 1755111596180526, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596181597, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596181851, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596182441, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596183340, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596184040, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596184619, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596184900, "dur": 1680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755111596186581, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596186698, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596186871, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596187077, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596187135, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755111596188132, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596188441, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_8DA00406C36CC6AA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755111596188587, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1755111596188808, "dur": 136461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596325270, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755111596328284, "dur": 2864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755111596331190, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755111596333929, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596334070, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596334348, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596334499, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596334655, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1755111596334787, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596334949, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596335350, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755111596335847, "dur": 616890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596129239, "dur": 39263, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596168503, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_409E75408550DFD9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169069, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596169165, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_EF946CA7674E0325.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169327, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C89BF2893952DD2D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169419, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A9459742FC2B00F9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169509, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_129E2D525C6714B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169599, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_129E2D525C6714B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169706, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_32939C22C21A9A23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169816, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_31B99CCAA2C60608.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169924, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5267BC5A847D8B9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596169976, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596170162, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C3A5EBA3221A6287.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596170252, "dur": 242, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C3A5EBA3221A6287.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596170497, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_36711FC8F966584C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596170558, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596170617, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596170793, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_A033713F460B0D20.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596170893, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596171014, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596171504, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755111596171755, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1755111596172053, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596172543, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596172926, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596173012, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596173110, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596173581, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596174056, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16942202937359522267.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755111596174512, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596176274, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596178680, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596179165, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596179649, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596180015, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596180946, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91e7def251e0\\Editor\\UI\\BoolFieldOverlayPopupWindow.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755111596180604, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596181772, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596182316, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596182923, "dur": 1096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596184051, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596184824, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596185040, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755111596186783, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596186863, "dur": 944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755111596187807, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596187963, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ParrelSync.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596188205, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596188393, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755111596188493, "dur": 304, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1755111596188798, "dur": 136473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596325272, "dur": 2730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111596328069, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111596330594, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755111596330670, "dur": 2413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111596333139, "dur": 2636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755111596335850, "dur": 616912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596128643, "dur": 39734, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596168386, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_B430B0CD9867C792.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596168964, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_22C73FD68F7437D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596169031, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596169112, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596169276, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_66BBE97858D0953F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596169372, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0BCE78C25C474DFA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596169429, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596169612, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0BCE78C25C474DFA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596169803, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EFF9361EDEBC0A6E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596169900, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596169983, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596170209, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3D6AFCA43E0B6878.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596170322, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BC1462186899290E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596170457, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_22355095CF1A36B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596170621, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_81542F95A0721D6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596170711, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1755111596171183, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1755111596171626, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1755111596172077, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596172492, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596172932, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596173188, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1755111596173415, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596173475, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596173558, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596173877, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596174228, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2210688342310731613.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596174411, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3648465134513896125.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755111596174545, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596176191, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596177485, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596177759, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596178014, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596178408, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596178807, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596179283, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596179606, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596179922, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596180971, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91e7def251e0\\Editor\\UI\\TilePaletteClipboardButton.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755111596180446, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596181501, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596181773, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596182000, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596182399, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596183240, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596184033, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596184608, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596184924, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596185115, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111596186171, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596186238, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596186387, "dur": 327, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_DDBAA27A82CE9E28.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596186739, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596186969, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596187180, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111596188047, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596188515, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596188737, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755111596188802, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755111596189462, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596189595, "dur": 135697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596325295, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755111596328292, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755111596331103, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596331170, "dur": 2948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755111596334119, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596334284, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596334555, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596334646, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1755111596334916, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596335036, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596335544, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755111596336093, "dur": 616665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596128522, "dur": 39843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596168373, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_4D4853D2BE4AE8AE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596168890, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596168953, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_F9331591B1619B3E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169076, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_E221DA127803E46F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169159, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596169278, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0CAABF378ACEABEF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169368, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0CAABF378ACEABEF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169448, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169555, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596169635, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7FE145A456DFCBEF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169714, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596169809, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_74816C903F3AF197.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596169987, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596170199, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_403651568F253731.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596170381, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_FB367C9346FF9A5D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596170437, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_728920FEC952B3E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596170531, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596170699, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596170789, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_BC3370F59F28D3D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596170900, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596171527, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596172025, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596172413, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596172948, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596173184, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1755111596173355, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596173459, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596173766, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596174069, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596174353, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12697856024727907050.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755111596174749, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596175024, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596176324, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596177581, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596177842, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596178103, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596178643, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596179100, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596179435, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596179943, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596180942, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91e7def251e0\\Editor\\EditorTools\\BoxTool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1755111596180714, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596181922, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596182384, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596183234, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596184047, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596184632, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596184952, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596185012, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755111596186020, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596186243, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596186329, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596186600, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755111596187408, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596187834, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755111596188053, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755111596188681, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596188816, "dur": 136450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596325268, "dur": 2745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755111596328055, "dur": 4487, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755111596332545, "dur": 2923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755111596335547, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755111596336161, "dur": 616600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596128765, "dur": 39631, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596168408, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B147A254CFD2102C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596169027, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D8824977E7F2B23E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596169171, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596169253, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A3DF7B62ADBC8E51.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596169388, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596169479, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7B597B5EE040FE53.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596169574, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7B597B5EE040FE53.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596169699, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2AE61BEFE92D422E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596169786, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596169850, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BBF013107B6E7683.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596170190, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_84C61B5FFF6AE5F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596170287, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_EDF98A79C83CDC6C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596170376, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_70F846A33051C353.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596170674, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596170801, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_2CA09514599DD5F0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596170911, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1755111596171195, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596171857, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1755111596172225, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596172835, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596172973, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596173044, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596173321, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596173564, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596174059, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596174145, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596174345, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7634377931854478575.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755111596174607, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596176314, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596178064, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596178638, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596179343, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596179742, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596180175, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596180983, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@a1146c20a947\\Editor\\ObjectMenuCreation\\ItemCreationUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755111596180960, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596181937, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596182351, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596183256, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596184056, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596184634, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596184857, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596185170, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596186013, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596186246, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596186597, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596186665, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596187777, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596188397, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596188642, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596188759, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596189168, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755111596189370, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596189878, "dur": 135395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596325275, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596327771, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596327874, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596330477, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596330677, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596333340, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755111596333537, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755111596336080, "dur": 616661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596128884, "dur": 39526, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596168419, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B428977015ACB395.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596168997, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2E8623D16E8E668C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596169076, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596169416, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_8824D3AD2222F26E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596169472, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596169591, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1F0AB14E18D23E11.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596169650, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596169755, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1F0AB14E18D23E11.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596169942, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596169992, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596170149, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596170286, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_744130CDF3C1ADFA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596170384, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_744130CDF3C1ADFA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596170455, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_AE7BD4FEA5F1A611.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596170581, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596170633, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_AE7BD4FEA5F1A611.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596170739, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755111596171149, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755111596171345, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111596171768, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755111596172131, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111596172643, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1755111596172968, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111596173309, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1755111596173447, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111596173754, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111596173934, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755111596174520, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596176487, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596178406, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596178826, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596179232, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596179515, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596179866, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596180654, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596181847, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596182283, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596182555, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596183501, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596184029, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596184612, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596184857, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596185036, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596186117, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596186190, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596186416, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596187317, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596187598, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596188365, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596188437, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596188569, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755111596188789, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596189372, "dur": 135892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596325266, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596328100, "dur": 2717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596330863, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ParrelSync.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755111596333505, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596334191, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596334662, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1755111596334859, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596335051, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596335534, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755111596336036, "dur": 616724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596128942, "dur": 39480, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596168430, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1A6A88D66EF4C557.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596169053, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596169188, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596169429, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F12DCDB48BAEBA17.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596169537, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596169725, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2D8413BDE13CD7F1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596169812, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_58C2D2C6FDB49C32.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596169929, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_657053D7FE219A28.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596170007, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596170175, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_C763F25127858F24.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596170285, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_C763F25127858F24.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596170359, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_88B40F493262C216.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596170433, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596170711, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596170887, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596171369, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755111596171629, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755111596172049, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596172481, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596172671, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755111596172965, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596173239, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755111596173312, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755111596173444, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596173806, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596174257, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755111596174481, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596175028, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596177496, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.HttpSys.dll"}}, {"pid": 12345, "tid": 8, "ts": 1755111596176261, "dur": 2122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596178384, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596178918, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596179241, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596179520, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596180938, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Runtime\\TMP\\FastAction.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755111596180107, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596181535, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596182210, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596182799, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596182943, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596184022, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596184651, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596184866, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596184984, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596185763, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596185906, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596186115, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596187676, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596187818, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596188454, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596188546, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_BCD600250821B9C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596188622, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755111596188896, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596189420, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596189652, "dur": 135632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596325286, "dur": 2997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596328284, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596328344, "dur": 2543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596330888, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755111596330945, "dur": 2479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596333477, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755111596336139, "dur": 616624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596129050, "dur": 39387, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596168447, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596169045, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_201A952097D16B89.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596169109, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596169199, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_00FC4C225939AAE2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596169316, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_52799C164F48C7C2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596169391, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596169508, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_52799C164F48C7C2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596169560, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_C81CFFEB4B719421.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596169761, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596170045, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E714CD2BFF109E7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596170284, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_441A482B34BB1A1B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596170400, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_14BBB0A1A56B54F9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596170669, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_14BBB0A1A56B54F9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596170747, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755111596171163, "dur": 11654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596182890, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596183015, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596183185, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596184071, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596184186, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596184607, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596184848, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596185532, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596185863, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596185913, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596186173, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596186403, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596187420, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596187501, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596187641, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755111596187920, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596188659, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1755111596189374, "dur": 173, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596189897, "dur": 126086, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1755111596325275, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596328061, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755111596328155, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596330852, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596333533, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755111596336018, "dur": 616712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596129091, "dur": 39362, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596168462, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_3497E18D9ACE6C53.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596169086, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_12994AB43863D35B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596169280, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CE9F463A1284211A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596169388, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_810264E72E8EDE2D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596169470, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5AAA2FC2CAC01A77.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596169633, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596169704, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596169757, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596170056, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_284B36A6DA56D348.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596170327, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596170460, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_8A77BBF0F2259C97.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596170687, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596170926, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755111596171234, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596171871, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596172367, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1755111596172805, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596173245, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1755111596173421, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596173675, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ParrelSync.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596173944, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596174192, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755111596174504, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596176321, "dur": 2041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596178363, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596178788, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596179152, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596179602, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596180932, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Meters\\IStopWatch.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755111596180127, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596181620, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596182013, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596182635, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596182969, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596184023, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596184609, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596185001, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596185107, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755111596186422, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596186748, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596186844, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755111596187158, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755111596187999, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596188224, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596188351, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ParrelSync.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755111596188976, "dur": 136323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596325300, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111596328034, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596328094, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111596330754, "dur": 2120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596332886, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755111596335533, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755111596335839, "dur": 616892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596129124, "dur": 39344, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596168480, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3B4D13D712F00EB8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596169089, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B3BE1B8334830D52.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596169225, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B3BE1B8334830D52.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596169323, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A42BD3F60EFF66D9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596172102, "dur": 358, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 11, "ts": 1755111596172460, "dur": 2031, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 11, "ts": 1755111596174492, "dur": 135, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 11, "ts": 1755111596169502, "dur": 5125, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596174628, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596176519, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596176728, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596177086, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596177335, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596177582, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596177845, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596178113, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596178529, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596179034, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596179572, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596180020, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596180968, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@a1146c20a947\\Editor\\SpriteEditor\\SpriteRect.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755111596180815, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596181744, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596182250, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596182799, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596182937, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596184026, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596184614, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596184805, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596185110, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596185749, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596185873, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596186107, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596187061, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596187378, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596187653, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596188459, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596189159, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596189465, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596190039, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596190230, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596190893, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755111596191022, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596191654, "dur": 50, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596192286, "dur": 201419, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596399913, "dur": 8961, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755111596399900, "dur": 10436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596412272, "dur": 554, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755111596413355, "dur": 506097, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755111596946638, "dur": 5336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755111596946627, "dur": 5350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1755111596951997, "dur": 691, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755111596129166, "dur": 39319, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596168495, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169094, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596169194, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_29BCCE2A849A4ABC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169289, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_316ABDB813889A82.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169426, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596169580, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7C053C2C91326FF8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169716, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7C053C2C91326FF8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169783, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C7ED8541370EC3E2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169885, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FC3813CDB6AE67C0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596169994, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_CCD38C9CCE3A7CB2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596170206, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_CCD38C9CCE3A7CB2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596170270, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_0BFC51EF888A92A4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596170365, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F03F48A1E46251D9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596170671, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596170866, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596170961, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596171360, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596172029, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596172472, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596173003, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596173463, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596173758, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596174078, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596174207, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13445476680238899994.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755111596174551, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596176452, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596178751, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596179094, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596179437, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596180058, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596180931, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91e7def251e0\\Editor\\EditorTools\\PaintTool.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755111596180660, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596181572, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596182200, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596182570, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596183419, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596184032, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596184606, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596184806, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596185675, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596185773, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596186018, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596186190, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596186316, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596186402, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596187096, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596187174, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596187354, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596187486, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596188256, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.Editor.ref.dll_500201FE28D4C516.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596188412, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755111596188623, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596189246, "dur": 136029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596325276, "dur": 3329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596328606, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596328834, "dur": 2759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596331656, "dur": 2808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755111596334663, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755111596334956, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596335556, "dur": 611102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755111596946682, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1755111596946659, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1755111596946816, "dur": 1638, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1755111596948458, "dur": 4276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755111596959981, "dur": 1501, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 32048, "tid": 206, "ts": 1755111596980967, "dur": 2511, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 32048, "tid": 206, "ts": 1755111596983519, "dur": 2430, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 32048, "tid": 206, "ts": 1755111596975578, "dur": 11333, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}